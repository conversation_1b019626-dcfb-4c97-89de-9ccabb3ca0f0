{"ast": null, "code": "import { genStyleHooks, mergeToken } from '../../theme/internal';\nimport { initComponentToken, initInputToken } from './token';\nexport { initComponentToken, initInputToken };\nconst genTextAreaStyle = token => {\n  const {\n    componentCls,\n    paddingLG\n  } = token;\n  const textareaPrefixCls = `${componentCls}-textarea`;\n  return {\n    // Raw Textarea\n    [`textarea${componentCls}`]: {\n      maxWidth: '100%',\n      // prevent textarea resize from coming out of its container\n      height: 'auto',\n      minHeight: token.controlHeight,\n      lineHeight: token.lineHeight,\n      verticalAlign: 'bottom',\n      transition: `all ${token.motionDurationSlow}`,\n      resize: 'vertical',\n      [`&${componentCls}-mouse-active`]: {\n        transition: `all ${token.motionDurationSlow}, height 0s, width 0s`\n      }\n    },\n    // Wrapper for resize\n    [`${componentCls}-textarea-affix-wrapper-resize-dirty`]: {\n      width: 'auto'\n    },\n    [textareaPrefixCls]: {\n      position: 'relative',\n      '&-show-count': {\n        [`${componentCls}-data-count`]: {\n          position: 'absolute',\n          bottom: token.calc(token.fontSize).mul(token.lineHeight).mul(-1).equal(),\n          insetInlineEnd: 0,\n          color: token.colorTextDescription,\n          whiteSpace: 'nowrap',\n          pointerEvents: 'none'\n        }\n      },\n      [`\n        &-allow-clear > ${componentCls},\n        &-affix-wrapper${textareaPrefixCls}-has-feedback ${componentCls}\n      `]: {\n        paddingInlineEnd: paddingLG\n      },\n      [`&-affix-wrapper${componentCls}-affix-wrapper`]: {\n        padding: 0,\n        [`> textarea${componentCls}`]: {\n          fontSize: 'inherit',\n          border: 'none',\n          outline: 'none',\n          background: 'transparent',\n          minHeight: token.calc(token.controlHeight).sub(token.calc(token.lineWidth).mul(2)).equal(),\n          '&:focus': {\n            boxShadow: 'none !important'\n          }\n        },\n        [`${componentCls}-suffix`]: {\n          margin: 0,\n          '> *:not(:last-child)': {\n            marginInline: 0\n          },\n          // Clear Icon\n          [`${componentCls}-clear-icon`]: {\n            position: 'absolute',\n            insetInlineEnd: token.paddingInline,\n            insetBlockStart: token.paddingXS\n          },\n          // Feedback Icon\n          [`${textareaPrefixCls}-suffix`]: {\n            position: 'absolute',\n            top: 0,\n            insetInlineEnd: token.paddingInline,\n            bottom: 0,\n            zIndex: 1,\n            display: 'inline-flex',\n            alignItems: 'center',\n            margin: 'auto',\n            pointerEvents: 'none'\n          }\n        }\n      },\n      [`&-affix-wrapper${componentCls}-affix-wrapper-rtl`]: {\n        [`${componentCls}-suffix`]: {\n          [`${componentCls}-data-count`]: {\n            direction: 'ltr',\n            insetInlineStart: 0\n          }\n        }\n      },\n      [`&-affix-wrapper${componentCls}-affix-wrapper-sm`]: {\n        [`${componentCls}-suffix`]: {\n          [`${componentCls}-clear-icon`]: {\n            insetInlineEnd: token.paddingInlineSM\n          }\n        }\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport default genStyleHooks(['Input', 'TextArea'], token => {\n  const inputToken = mergeToken(token, initInputToken(token));\n  return [genTextAreaStyle(inputToken)];\n}, initComponentToken, {\n  resetFont: false\n});", "map": {"version": 3, "names": ["genStyleHooks", "mergeToken", "initComponentToken", "initInputToken", "genTextAreaStyle", "token", "componentCls", "paddingLG", "textareaPrefixCls", "max<PERSON><PERSON><PERSON>", "height", "minHeight", "controlHeight", "lineHeight", "verticalAlign", "transition", "motionDurationSlow", "resize", "width", "position", "bottom", "calc", "fontSize", "mul", "equal", "insetInlineEnd", "color", "colorTextDescription", "whiteSpace", "pointerEvents", "paddingInlineEnd", "padding", "border", "outline", "background", "sub", "lineWidth", "boxShadow", "margin", "marginInline", "paddingInline", "insetBlockStart", "paddingXS", "top", "zIndex", "display", "alignItems", "direction", "insetInlineStart", "paddingInlineSM", "inputToken", "resetFont"], "sources": ["/Users/<USER>/Desktop/code/system/frontend/node_modules/antd/es/input/style/textarea.js"], "sourcesContent": ["import { genStyleHooks, mergeToken } from '../../theme/internal';\nimport { initComponentToken, initInputToken } from './token';\nexport { initComponentToken, initInputToken };\nconst genTextAreaStyle = token => {\n  const {\n    componentCls,\n    paddingLG\n  } = token;\n  const textareaPrefixCls = `${componentCls}-textarea`;\n  return {\n    // Raw Textarea\n    [`textarea${componentCls}`]: {\n      maxWidth: '100%',\n      // prevent textarea resize from coming out of its container\n      height: 'auto',\n      minHeight: token.controlHeight,\n      lineHeight: token.lineHeight,\n      verticalAlign: 'bottom',\n      transition: `all ${token.motionDurationSlow}`,\n      resize: 'vertical',\n      [`&${componentCls}-mouse-active`]: {\n        transition: `all ${token.motionDurationSlow}, height 0s, width 0s`\n      }\n    },\n    // Wrapper for resize\n    [`${componentCls}-textarea-affix-wrapper-resize-dirty`]: {\n      width: 'auto'\n    },\n    [textareaPrefixCls]: {\n      position: 'relative',\n      '&-show-count': {\n        [`${componentCls}-data-count`]: {\n          position: 'absolute',\n          bottom: token.calc(token.fontSize).mul(token.lineHeight).mul(-1).equal(),\n          insetInlineEnd: 0,\n          color: token.colorTextDescription,\n          whiteSpace: 'nowrap',\n          pointerEvents: 'none'\n        }\n      },\n      [`\n        &-allow-clear > ${componentCls},\n        &-affix-wrapper${textareaPrefixCls}-has-feedback ${componentCls}\n      `]: {\n        paddingInlineEnd: paddingLG\n      },\n      [`&-affix-wrapper${componentCls}-affix-wrapper`]: {\n        padding: 0,\n        [`> textarea${componentCls}`]: {\n          fontSize: 'inherit',\n          border: 'none',\n          outline: 'none',\n          background: 'transparent',\n          minHeight: token.calc(token.controlHeight).sub(token.calc(token.lineWidth).mul(2)).equal(),\n          '&:focus': {\n            boxShadow: 'none !important'\n          }\n        },\n        [`${componentCls}-suffix`]: {\n          margin: 0,\n          '> *:not(:last-child)': {\n            marginInline: 0\n          },\n          // Clear Icon\n          [`${componentCls}-clear-icon`]: {\n            position: 'absolute',\n            insetInlineEnd: token.paddingInline,\n            insetBlockStart: token.paddingXS\n          },\n          // Feedback Icon\n          [`${textareaPrefixCls}-suffix`]: {\n            position: 'absolute',\n            top: 0,\n            insetInlineEnd: token.paddingInline,\n            bottom: 0,\n            zIndex: 1,\n            display: 'inline-flex',\n            alignItems: 'center',\n            margin: 'auto',\n            pointerEvents: 'none'\n          }\n        }\n      },\n      [`&-affix-wrapper${componentCls}-affix-wrapper-rtl`]: {\n        [`${componentCls}-suffix`]: {\n          [`${componentCls}-data-count`]: {\n            direction: 'ltr',\n            insetInlineStart: 0\n          }\n        }\n      },\n      [`&-affix-wrapper${componentCls}-affix-wrapper-sm`]: {\n        [`${componentCls}-suffix`]: {\n          [`${componentCls}-clear-icon`]: {\n            insetInlineEnd: token.paddingInlineSM\n          }\n        }\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport default genStyleHooks(['Input', 'TextArea'], token => {\n  const inputToken = mergeToken(token, initInputToken(token));\n  return [genTextAreaStyle(inputToken)];\n}, initComponentToken, {\n  resetFont: false\n});"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,SAAS;AAC5D,SAASD,kBAAkB,EAAEC,cAAc;AAC3C,MAAMC,gBAAgB,GAAGC,KAAK,IAAI;EAChC,MAAM;IACJC,YAAY;IACZC;EACF,CAAC,GAAGF,KAAK;EACT,MAAMG,iBAAiB,GAAG,GAAGF,YAAY,WAAW;EACpD,OAAO;IACL;IACA,CAAC,WAAWA,YAAY,EAAE,GAAG;MAC3BG,QAAQ,EAAE,MAAM;MAChB;MACAC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAEN,KAAK,CAACO,aAAa;MAC9BC,UAAU,EAAER,KAAK,CAACQ,UAAU;MAC5BC,aAAa,EAAE,QAAQ;MACvBC,UAAU,EAAE,OAAOV,KAAK,CAACW,kBAAkB,EAAE;MAC7CC,MAAM,EAAE,UAAU;MAClB,CAAC,IAAIX,YAAY,eAAe,GAAG;QACjCS,UAAU,EAAE,OAAOV,KAAK,CAACW,kBAAkB;MAC7C;IACF,CAAC;IACD;IACA,CAAC,GAAGV,YAAY,sCAAsC,GAAG;MACvDY,KAAK,EAAE;IACT,CAAC;IACD,CAACV,iBAAiB,GAAG;MACnBW,QAAQ,EAAE,UAAU;MACpB,cAAc,EAAE;QACd,CAAC,GAAGb,YAAY,aAAa,GAAG;UAC9Ba,QAAQ,EAAE,UAAU;UACpBC,MAAM,EAAEf,KAAK,CAACgB,IAAI,CAAChB,KAAK,CAACiB,QAAQ,CAAC,CAACC,GAAG,CAAClB,KAAK,CAACQ,UAAU,CAAC,CAACU,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;UACxEC,cAAc,EAAE,CAAC;UACjBC,KAAK,EAAErB,KAAK,CAACsB,oBAAoB;UACjCC,UAAU,EAAE,QAAQ;UACpBC,aAAa,EAAE;QACjB;MACF,CAAC;MACD,CAAC;AACP,0BAA0BvB,YAAY;AACtC,yBAAyBE,iBAAiB,iBAAiBF,YAAY;AACvE,OAAO,GAAG;QACFwB,gBAAgB,EAAEvB;MACpB,CAAC;MACD,CAAC,kBAAkBD,YAAY,gBAAgB,GAAG;QAChDyB,OAAO,EAAE,CAAC;QACV,CAAC,aAAazB,YAAY,EAAE,GAAG;UAC7BgB,QAAQ,EAAE,SAAS;UACnBU,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,aAAa;UACzBvB,SAAS,EAAEN,KAAK,CAACgB,IAAI,CAAChB,KAAK,CAACO,aAAa,CAAC,CAACuB,GAAG,CAAC9B,KAAK,CAACgB,IAAI,CAAChB,KAAK,CAAC+B,SAAS,CAAC,CAACb,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;UAC1F,SAAS,EAAE;YACTa,SAAS,EAAE;UACb;QACF,CAAC;QACD,CAAC,GAAG/B,YAAY,SAAS,GAAG;UAC1BgC,MAAM,EAAE,CAAC;UACT,sBAAsB,EAAE;YACtBC,YAAY,EAAE;UAChB,CAAC;UACD;UACA,CAAC,GAAGjC,YAAY,aAAa,GAAG;YAC9Ba,QAAQ,EAAE,UAAU;YACpBM,cAAc,EAAEpB,KAAK,CAACmC,aAAa;YACnCC,eAAe,EAAEpC,KAAK,CAACqC;UACzB,CAAC;UACD;UACA,CAAC,GAAGlC,iBAAiB,SAAS,GAAG;YAC/BW,QAAQ,EAAE,UAAU;YACpBwB,GAAG,EAAE,CAAC;YACNlB,cAAc,EAAEpB,KAAK,CAACmC,aAAa;YACnCpB,MAAM,EAAE,CAAC;YACTwB,MAAM,EAAE,CAAC;YACTC,OAAO,EAAE,aAAa;YACtBC,UAAU,EAAE,QAAQ;YACpBR,MAAM,EAAE,MAAM;YACdT,aAAa,EAAE;UACjB;QACF;MACF,CAAC;MACD,CAAC,kBAAkBvB,YAAY,oBAAoB,GAAG;QACpD,CAAC,GAAGA,YAAY,SAAS,GAAG;UAC1B,CAAC,GAAGA,YAAY,aAAa,GAAG;YAC9ByC,SAAS,EAAE,KAAK;YAChBC,gBAAgB,EAAE;UACpB;QACF;MACF,CAAC;MACD,CAAC,kBAAkB1C,YAAY,mBAAmB,GAAG;QACnD,CAAC,GAAGA,YAAY,SAAS,GAAG;UAC1B,CAAC,GAAGA,YAAY,aAAa,GAAG;YAC9BmB,cAAc,EAAEpB,KAAK,CAAC4C;UACxB;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD;AACA,eAAejD,aAAa,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,EAAEK,KAAK,IAAI;EAC3D,MAAM6C,UAAU,GAAGjD,UAAU,CAACI,KAAK,EAAEF,cAAc,CAACE,KAAK,CAAC,CAAC;EAC3D,OAAO,CAACD,gBAAgB,CAAC8C,UAAU,CAAC,CAAC;AACvC,CAAC,EAAEhD,kBAAkB,EAAE;EACrBiD,SAAS,EAAE;AACb,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}