{"ast": null, "code": "import * as React from 'react';\nimport { composeRef, getNodeRef } from \"rc-util/es/ref\";\nimport { FormContext } from '../context';\nexport default function useItemRef() {\n  const {\n    itemRef\n  } = React.useContext(FormContext);\n  const cacheRef = React.useRef({});\n  function getRef(name, children) {\n    // Outer caller already check the `supportRef`\n    const childrenRef = children && typeof children === 'object' && getNodeRef(children);\n    const nameStr = name.join('_');\n    if (cacheRef.current.name !== nameStr || cacheRef.current.originRef !== childrenRef) {\n      cacheRef.current.name = nameStr;\n      cacheRef.current.originRef = childrenRef;\n      cacheRef.current.ref = composeRef(itemRef(name), childrenRef);\n    }\n    return cacheRef.current.ref;\n  }\n  return getRef;\n}", "map": {"version": 3, "names": ["React", "composeRef", "getNodeRef", "FormContext", "useItemRef", "itemRef", "useContext", "cacheRef", "useRef", "getRef", "name", "children", "childrenRef", "nameStr", "join", "current", "originRef", "ref"], "sources": ["/Users/<USER>/Desktop/code/system/frontend/node_modules/antd/es/form/hooks/useItemRef.js"], "sourcesContent": ["import * as React from 'react';\nimport { composeRef, getNodeRef } from \"rc-util/es/ref\";\nimport { FormContext } from '../context';\nexport default function useItemRef() {\n  const {\n    itemRef\n  } = React.useContext(FormContext);\n  const cacheRef = React.useRef({});\n  function getRef(name, children) {\n    // Outer caller already check the `supportRef`\n    const childrenRef = children && typeof children === 'object' && getNodeRef(children);\n    const nameStr = name.join('_');\n    if (cacheRef.current.name !== nameStr || cacheRef.current.originRef !== childrenRef) {\n      cacheRef.current.name = nameStr;\n      cacheRef.current.originRef = childrenRef;\n      cacheRef.current.ref = composeRef(itemRef(name), childrenRef);\n    }\n    return cacheRef.current.ref;\n  }\n  return getRef;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,UAAU,QAAQ,gBAAgB;AACvD,SAASC,WAAW,QAAQ,YAAY;AACxC,eAAe,SAASC,UAAUA,CAAA,EAAG;EACnC,MAAM;IACJC;EACF,CAAC,GAAGL,KAAK,CAACM,UAAU,CAACH,WAAW,CAAC;EACjC,MAAMI,QAAQ,GAAGP,KAAK,CAACQ,MAAM,CAAC,CAAC,CAAC,CAAC;EACjC,SAASC,MAAMA,CAACC,IAAI,EAAEC,QAAQ,EAAE;IAC9B;IACA,MAAMC,WAAW,GAAGD,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAIT,UAAU,CAACS,QAAQ,CAAC;IACpF,MAAME,OAAO,GAAGH,IAAI,CAACI,IAAI,CAAC,GAAG,CAAC;IAC9B,IAAIP,QAAQ,CAACQ,OAAO,CAACL,IAAI,KAAKG,OAAO,IAAIN,QAAQ,CAACQ,OAAO,CAACC,SAAS,KAAKJ,WAAW,EAAE;MACnFL,QAAQ,CAACQ,OAAO,CAACL,IAAI,GAAGG,OAAO;MAC/BN,QAAQ,CAACQ,OAAO,CAACC,SAAS,GAAGJ,WAAW;MACxCL,QAAQ,CAACQ,OAAO,CAACE,GAAG,GAAGhB,UAAU,CAACI,OAAO,CAACK,IAAI,CAAC,EAAEE,WAAW,CAAC;IAC/D;IACA,OAAOL,QAAQ,CAACQ,OAAO,CAACE,GAAG;EAC7B;EACA,OAAOR,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}