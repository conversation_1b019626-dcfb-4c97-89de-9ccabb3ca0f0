{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genPickerStyle = token => {\n  const {\n    componentCls,\n    controlHeightLG,\n    borderRadiusSM,\n    colorPickerInsetShadow,\n    marginSM,\n    colorBgElevated,\n    colorFillSecondary,\n    lineWidthBold,\n    colorPickerHandlerSize\n  } = token;\n  return {\n    userSelect: 'none',\n    [`${componentCls}-select`]: {\n      [`${componentCls}-palette`]: {\n        minHeight: token.calc(controlHeightLG).mul(4).equal(),\n        overflow: 'hidden',\n        borderRadius: borderRadiusSM\n      },\n      [`${componentCls}-saturation`]: {\n        position: 'absolute',\n        borderRadius: 'inherit',\n        boxShadow: colorPickerInsetShadow,\n        inset: 0\n      },\n      marginBottom: marginSM\n    },\n    // ======================== Panel =========================\n    [`${componentCls}-handler`]: {\n      width: colorPickerHandlerSize,\n      height: colorPickerHandlerSize,\n      border: `${unit(lineWidthBold)} solid ${colorBgElevated}`,\n      position: 'relative',\n      borderRadius: '50%',\n      cursor: 'pointer',\n      boxShadow: `${colorPickerInsetShadow}, 0 0 0 1px ${colorFillSecondary}`\n    }\n  };\n};\nexport default genPickerStyle;", "map": {"version": 3, "names": ["unit", "genPickerStyle", "token", "componentCls", "controlHeightLG", "borderRadiusSM", "colorPickerInsetShadow", "marginSM", "colorBgElevated", "colorFillSecondary", "lineWidthBold", "colorPickerHandlerSize", "userSelect", "minHeight", "calc", "mul", "equal", "overflow", "borderRadius", "position", "boxShadow", "inset", "marginBottom", "width", "height", "border", "cursor"], "sources": ["/Users/<USER>/Desktop/code/system/frontend/node_modules/antd/es/color-picker/style/picker.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genPickerStyle = token => {\n  const {\n    componentCls,\n    controlHeightLG,\n    borderRadiusSM,\n    colorPickerInsetShadow,\n    marginSM,\n    colorBgElevated,\n    colorFillSecondary,\n    lineWidthBold,\n    colorPickerHandlerSize\n  } = token;\n  return {\n    userSelect: 'none',\n    [`${componentCls}-select`]: {\n      [`${componentCls}-palette`]: {\n        minHeight: token.calc(controlHeightLG).mul(4).equal(),\n        overflow: 'hidden',\n        borderRadius: borderRadiusSM\n      },\n      [`${componentCls}-saturation`]: {\n        position: 'absolute',\n        borderRadius: 'inherit',\n        boxShadow: colorPickerInsetShadow,\n        inset: 0\n      },\n      marginBottom: marginSM\n    },\n    // ======================== Panel =========================\n    [`${componentCls}-handler`]: {\n      width: colorPickerHandlerSize,\n      height: colorPickerHandlerSize,\n      border: `${unit(lineWidthBold)} solid ${colorBgElevated}`,\n      position: 'relative',\n      borderRadius: '50%',\n      cursor: 'pointer',\n      boxShadow: `${colorPickerInsetShadow}, 0 0 0 1px ${colorFillSecondary}`\n    }\n  };\n};\nexport default genPickerStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,cAAc,GAAGC,KAAK,IAAI;EAC9B,MAAM;IACJC,YAAY;IACZC,eAAe;IACfC,cAAc;IACdC,sBAAsB;IACtBC,QAAQ;IACRC,eAAe;IACfC,kBAAkB;IAClBC,aAAa;IACbC;EACF,CAAC,GAAGT,KAAK;EACT,OAAO;IACLU,UAAU,EAAE,MAAM;IAClB,CAAC,GAAGT,YAAY,SAAS,GAAG;MAC1B,CAAC,GAAGA,YAAY,UAAU,GAAG;QAC3BU,SAAS,EAAEX,KAAK,CAACY,IAAI,CAACV,eAAe,CAAC,CAACW,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;QACrDC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAEb;MAChB,CAAC;MACD,CAAC,GAAGF,YAAY,aAAa,GAAG;QAC9BgB,QAAQ,EAAE,UAAU;QACpBD,YAAY,EAAE,SAAS;QACvBE,SAAS,EAAEd,sBAAsB;QACjCe,KAAK,EAAE;MACT,CAAC;MACDC,YAAY,EAAEf;IAChB,CAAC;IACD;IACA,CAAC,GAAGJ,YAAY,UAAU,GAAG;MAC3BoB,KAAK,EAAEZ,sBAAsB;MAC7Ba,MAAM,EAAEb,sBAAsB;MAC9Bc,MAAM,EAAE,GAAGzB,IAAI,CAACU,aAAa,CAAC,UAAUF,eAAe,EAAE;MACzDW,QAAQ,EAAE,UAAU;MACpBD,YAAY,EAAE,KAAK;MACnBQ,MAAM,EAAE,SAAS;MACjBN,SAAS,EAAE,GAAGd,sBAAsB,eAAeG,kBAAkB;IACvE;EACF,CAAC;AACH,CAAC;AACD,eAAeR,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}