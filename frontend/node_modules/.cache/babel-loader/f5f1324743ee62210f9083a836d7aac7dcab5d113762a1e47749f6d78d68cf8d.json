{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/code/system/frontend/src/contexts/AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport authService from '../services/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    const initAuth = () => {\n      const currentUser = authService.getCurrentUser();\n      setUser(currentUser);\n      setLoading(false);\n    };\n    initAuth();\n  }, []);\n  const login = async (username, password) => {\n    try {\n      const response = await authService.login({\n        username,\n        password\n      });\n      const userData = {\n        id: response.id,\n        username: response.username,\n        email: response.email,\n        roles: response.roles\n      };\n      setUser(userData);\n    } catch (error) {\n      throw error;\n    }\n  };\n  const logout = async () => {\n    try {\n      await authService.logout();\n    } finally {\n      setUser(null);\n    }\n  };\n  const value = {\n    user,\n    isAuthenticated: !!user,\n    isAdmin: authService.isAdmin(),\n    login,\n    logout,\n    loading\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "authService", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "initAuth", "currentUser", "getCurrentUser", "login", "username", "password", "response", "userData", "id", "email", "roles", "error", "logout", "value", "isAuthenticated", "isAdmin", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/code/system/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport authService, { User } from '../services/authService';\n\ninterface AuthContextType {\n  user: User | null;\n  isAuthenticated: boolean;\n  isAdmin: boolean;\n  login: (username: string, password: string) => Promise<void>;\n  logout: () => Promise<void>;\n  loading: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const initAuth = () => {\n      const currentUser = authService.getCurrentUser();\n      setUser(currentUser);\n      setLoading(false);\n    };\n\n    initAuth();\n  }, []);\n\n  const login = async (username: string, password: string) => {\n    try {\n      const response = await authService.login({ username, password });\n      const userData: User = {\n        id: response.id,\n        username: response.username,\n        email: response.email,\n        roles: response.roles,\n      };\n      setUser(userData);\n    } catch (error) {\n      throw error;\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await authService.logout();\n    } finally {\n      setUser(null);\n    }\n  };\n\n  const value: AuthContextType = {\n    user,\n    isAuthenticated: !!user,\n    isAdmin: authService.isAdmin(),\n    login,\n    logout,\n    loading,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAmB,OAAO;AACxF,OAAOC,WAAW,MAAgB,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAW5D,MAAMC,WAAW,gBAAGP,aAAa,CAA8BQ,SAAS,CAAC;AAEzE,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGV,UAAU,CAACM,WAAW,CAAC;EACvC,IAAII,OAAO,KAAKH,SAAS,EAAE;IACzB,MAAM,IAAII,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAYpB,OAAO,MAAMI,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACzE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGf,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,MAAMiB,QAAQ,GAAGA,CAAA,KAAM;MACrB,MAAMC,WAAW,GAAGjB,WAAW,CAACkB,cAAc,CAAC,CAAC;MAChDL,OAAO,CAACI,WAAW,CAAC;MACpBF,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDC,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,KAAK,GAAG,MAAAA,CAAOC,QAAgB,EAAEC,QAAgB,KAAK;IAC1D,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMtB,WAAW,CAACmB,KAAK,CAAC;QAAEC,QAAQ;QAAEC;MAAS,CAAC,CAAC;MAChE,MAAME,QAAc,GAAG;QACrBC,EAAE,EAAEF,QAAQ,CAACE,EAAE;QACfJ,QAAQ,EAAEE,QAAQ,CAACF,QAAQ;QAC3BK,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBC,KAAK,EAAEJ,QAAQ,CAACI;MAClB,CAAC;MACDb,OAAO,CAACU,QAAQ,CAAC;IACnB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMC,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAM5B,WAAW,CAAC4B,MAAM,CAAC,CAAC;IAC5B,CAAC,SAAS;MACRf,OAAO,CAAC,IAAI,CAAC;IACf;EACF,CAAC;EAED,MAAMgB,KAAsB,GAAG;IAC7BjB,IAAI;IACJkB,eAAe,EAAE,CAAC,CAAClB,IAAI;IACvBmB,OAAO,EAAE/B,WAAW,CAAC+B,OAAO,CAAC,CAAC;IAC9BZ,KAAK;IACLS,MAAM;IACNd;EACF,CAAC;EAED,oBACEZ,OAAA,CAACC,WAAW,CAAC6B,QAAQ;IAACH,KAAK,EAAEA,KAAM;IAAAnB,QAAA,EAChCA;EAAQ;IAAAuB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACzB,GAAA,CAnDWF,YAAyC;AAAA4B,EAAA,GAAzC5B,YAAyC;AAAA,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}