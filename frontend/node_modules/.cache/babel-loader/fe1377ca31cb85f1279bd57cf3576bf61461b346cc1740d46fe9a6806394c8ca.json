{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useSize from '../config-provider/hooks/useSize';\nimport useStyle from './style';\nconst sizeClassNameMap = {\n  small: 'sm',\n  middle: 'md'\n};\nconst Divider = props => {\n  const {\n    getPrefixCls,\n    direction,\n    className: dividerClassName,\n    style: dividerStyle\n  } = useComponentConfig('divider');\n  const {\n      prefixCls: customizePrefixCls,\n      type = 'horizontal',\n      orientation = 'center',\n      orientationMargin,\n      className,\n      rootClassName,\n      children,\n      dashed,\n      variant = 'solid',\n      plain,\n      style,\n      size: customSize\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"type\", \"orientation\", \"orientationMargin\", \"className\", \"rootClassName\", \"children\", \"dashed\", \"variant\", \"plain\", \"style\", \"size\"]);\n  const prefixCls = getPrefixCls('divider', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const sizeFullName = useSize(customSize);\n  const sizeCls = sizeClassNameMap[sizeFullName];\n  const hasChildren = !!children;\n  const mergedOrientation = React.useMemo(() => {\n    if (orientation === 'left') {\n      return direction === 'rtl' ? 'end' : 'start';\n    }\n    if (orientation === 'right') {\n      return direction === 'rtl' ? 'start' : 'end';\n    }\n    return orientation;\n  }, [direction, orientation]);\n  const hasMarginStart = mergedOrientation === 'start' && orientationMargin != null;\n  const hasMarginEnd = mergedOrientation === 'end' && orientationMargin != null;\n  const classString = classNames(prefixCls, dividerClassName, hashId, cssVarCls, `${prefixCls}-${type}`, {\n    [`${prefixCls}-with-text`]: hasChildren,\n    [`${prefixCls}-with-text-${mergedOrientation}`]: hasChildren,\n    [`${prefixCls}-dashed`]: !!dashed,\n    [`${prefixCls}-${variant}`]: variant !== 'solid',\n    [`${prefixCls}-plain`]: !!plain,\n    [`${prefixCls}-rtl`]: direction === 'rtl',\n    [`${prefixCls}-no-default-orientation-margin-start`]: hasMarginStart,\n    [`${prefixCls}-no-default-orientation-margin-end`]: hasMarginEnd,\n    [`${prefixCls}-${sizeCls}`]: !!sizeCls\n  }, className, rootClassName);\n  const memoizedOrientationMargin = React.useMemo(() => {\n    if (typeof orientationMargin === 'number') {\n      return orientationMargin;\n    }\n    if (/^\\d+$/.test(orientationMargin)) {\n      return Number(orientationMargin);\n    }\n    return orientationMargin;\n  }, [orientationMargin]);\n  const innerStyle = {\n    marginInlineStart: hasMarginStart ? memoizedOrientationMargin : undefined,\n    marginInlineEnd: hasMarginEnd ? memoizedOrientationMargin : undefined\n  };\n  // Warning children not work in vertical mode\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Divider');\n    process.env.NODE_ENV !== \"production\" ? warning(!children || type !== 'vertical', 'usage', '`children` not working in `vertical` mode.') : void 0;\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", Object.assign({\n    className: classString,\n    style: Object.assign(Object.assign({}, dividerStyle), style)\n  }, restProps, {\n    role: \"separator\"\n  }), children && type !== 'vertical' && (/*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-inner-text`,\n    style: innerStyle\n  }, children))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Divider.displayName = 'Divider';\n}\nexport default Divider;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "devUseW<PERSON>ning", "useComponentConfig", "useSize", "useStyle", "sizeClassNameMap", "small", "middle", "Divider", "props", "getPrefixCls", "direction", "className", "dividerClassName", "style", "dividerStyle", "prefixCls", "customizePrefixCls", "type", "orientation", "<PERSON><PERSON><PERSON><PERSON>", "rootClassName", "children", "dashed", "variant", "plain", "size", "customSize", "restProps", "wrapCSSVar", "hashId", "cssVarCls", "sizeFullName", "sizeCls", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mergedOrientation", "useMemo", "hasMarginStart", "hasMarginEnd", "classString", "memoizedOrientationMargin", "test", "Number", "innerStyle", "marginInlineStart", "undefined", "marginInlineEnd", "process", "env", "NODE_ENV", "warning", "createElement", "assign", "role", "displayName"], "sources": ["/Users/<USER>/Desktop/code/system/frontend/node_modules/antd/es/divider/index.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useSize from '../config-provider/hooks/useSize';\nimport useStyle from './style';\nconst sizeClassNameMap = {\n  small: 'sm',\n  middle: 'md'\n};\nconst Divider = props => {\n  const {\n    getPrefixCls,\n    direction,\n    className: dividerClassName,\n    style: dividerStyle\n  } = useComponentConfig('divider');\n  const {\n      prefixCls: customizePrefixCls,\n      type = 'horizontal',\n      orientation = 'center',\n      orientationMargin,\n      className,\n      rootClassName,\n      children,\n      dashed,\n      variant = 'solid',\n      plain,\n      style,\n      size: customSize\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"type\", \"orientation\", \"orientationMargin\", \"className\", \"rootClassName\", \"children\", \"dashed\", \"variant\", \"plain\", \"style\", \"size\"]);\n  const prefixCls = getPrefixCls('divider', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const sizeFullName = useSize(customSize);\n  const sizeCls = sizeClassNameMap[sizeFullName];\n  const hasChildren = !!children;\n  const mergedOrientation = React.useMemo(() => {\n    if (orientation === 'left') {\n      return direction === 'rtl' ? 'end' : 'start';\n    }\n    if (orientation === 'right') {\n      return direction === 'rtl' ? 'start' : 'end';\n    }\n    return orientation;\n  }, [direction, orientation]);\n  const hasMarginStart = mergedOrientation === 'start' && orientationMargin != null;\n  const hasMarginEnd = mergedOrientation === 'end' && orientationMargin != null;\n  const classString = classNames(prefixCls, dividerClassName, hashId, cssVarCls, `${prefixCls}-${type}`, {\n    [`${prefixCls}-with-text`]: hasChildren,\n    [`${prefixCls}-with-text-${mergedOrientation}`]: hasChildren,\n    [`${prefixCls}-dashed`]: !!dashed,\n    [`${prefixCls}-${variant}`]: variant !== 'solid',\n    [`${prefixCls}-plain`]: !!plain,\n    [`${prefixCls}-rtl`]: direction === 'rtl',\n    [`${prefixCls}-no-default-orientation-margin-start`]: hasMarginStart,\n    [`${prefixCls}-no-default-orientation-margin-end`]: hasMarginEnd,\n    [`${prefixCls}-${sizeCls}`]: !!sizeCls\n  }, className, rootClassName);\n  const memoizedOrientationMargin = React.useMemo(() => {\n    if (typeof orientationMargin === 'number') {\n      return orientationMargin;\n    }\n    if (/^\\d+$/.test(orientationMargin)) {\n      return Number(orientationMargin);\n    }\n    return orientationMargin;\n  }, [orientationMargin]);\n  const innerStyle = {\n    marginInlineStart: hasMarginStart ? memoizedOrientationMargin : undefined,\n    marginInlineEnd: hasMarginEnd ? memoizedOrientationMargin : undefined\n  };\n  // Warning children not work in vertical mode\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Divider');\n    process.env.NODE_ENV !== \"production\" ? warning(!children || type !== 'vertical', 'usage', '`children` not working in `vertical` mode.') : void 0;\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", Object.assign({\n    className: classString,\n    style: Object.assign(Object.assign({}, dividerStyle), style)\n  }, restProps, {\n    role: \"separator\"\n  }), children && type !== 'vertical' && (/*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-inner-text`,\n    style: innerStyle\n  }, children))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Divider.displayName = 'Divider';\n}\nexport default Divider;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,OAAOC,OAAO,MAAM,kCAAkC;AACtD,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,gBAAgB,GAAG;EACvBC,KAAK,EAAE,IAAI;EACXC,MAAM,EAAE;AACV,CAAC;AACD,MAAMC,OAAO,GAAGC,KAAK,IAAI;EACvB,MAAM;IACJC,YAAY;IACZC,SAAS;IACTC,SAAS,EAAEC,gBAAgB;IAC3BC,KAAK,EAAEC;EACT,CAAC,GAAGb,kBAAkB,CAAC,SAAS,CAAC;EACjC,MAAM;MACFc,SAAS,EAAEC,kBAAkB;MAC7BC,IAAI,GAAG,YAAY;MACnBC,WAAW,GAAG,QAAQ;MACtBC,iBAAiB;MACjBR,SAAS;MACTS,aAAa;MACbC,QAAQ;MACRC,MAAM;MACNC,OAAO,GAAG,OAAO;MACjBC,KAAK;MACLX,KAAK;MACLY,IAAI,EAAEC;IACR,CAAC,GAAGlB,KAAK;IACTmB,SAAS,GAAG3C,MAAM,CAACwB,KAAK,EAAE,CAAC,WAAW,EAAE,MAAM,EAAE,aAAa,EAAE,mBAAmB,EAAE,WAAW,EAAE,eAAe,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;EAC/K,MAAMO,SAAS,GAAGN,YAAY,CAAC,SAAS,EAAEO,kBAAkB,CAAC;EAC7D,MAAM,CAACY,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAG3B,QAAQ,CAACY,SAAS,CAAC;EAC3D,MAAMgB,YAAY,GAAG7B,OAAO,CAACwB,UAAU,CAAC;EACxC,MAAMM,OAAO,GAAG5B,gBAAgB,CAAC2B,YAAY,CAAC;EAC9C,MAAME,WAAW,GAAG,CAAC,CAACZ,QAAQ;EAC9B,MAAMa,iBAAiB,GAAGpC,KAAK,CAACqC,OAAO,CAAC,MAAM;IAC5C,IAAIjB,WAAW,KAAK,MAAM,EAAE;MAC1B,OAAOR,SAAS,KAAK,KAAK,GAAG,KAAK,GAAG,OAAO;IAC9C;IACA,IAAIQ,WAAW,KAAK,OAAO,EAAE;MAC3B,OAAOR,SAAS,KAAK,KAAK,GAAG,OAAO,GAAG,KAAK;IAC9C;IACA,OAAOQ,WAAW;EACpB,CAAC,EAAE,CAACR,SAAS,EAAEQ,WAAW,CAAC,CAAC;EAC5B,MAAMkB,cAAc,GAAGF,iBAAiB,KAAK,OAAO,IAAIf,iBAAiB,IAAI,IAAI;EACjF,MAAMkB,YAAY,GAAGH,iBAAiB,KAAK,KAAK,IAAIf,iBAAiB,IAAI,IAAI;EAC7E,MAAMmB,WAAW,GAAGvC,UAAU,CAACgB,SAAS,EAAEH,gBAAgB,EAAEiB,MAAM,EAAEC,SAAS,EAAE,GAAGf,SAAS,IAAIE,IAAI,EAAE,EAAE;IACrG,CAAC,GAAGF,SAAS,YAAY,GAAGkB,WAAW;IACvC,CAAC,GAAGlB,SAAS,cAAcmB,iBAAiB,EAAE,GAAGD,WAAW;IAC5D,CAAC,GAAGlB,SAAS,SAAS,GAAG,CAAC,CAACO,MAAM;IACjC,CAAC,GAAGP,SAAS,IAAIQ,OAAO,EAAE,GAAGA,OAAO,KAAK,OAAO;IAChD,CAAC,GAAGR,SAAS,QAAQ,GAAG,CAAC,CAACS,KAAK;IAC/B,CAAC,GAAGT,SAAS,MAAM,GAAGL,SAAS,KAAK,KAAK;IACzC,CAAC,GAAGK,SAAS,sCAAsC,GAAGqB,cAAc;IACpE,CAAC,GAAGrB,SAAS,oCAAoC,GAAGsB,YAAY;IAChE,CAAC,GAAGtB,SAAS,IAAIiB,OAAO,EAAE,GAAG,CAAC,CAACA;EACjC,CAAC,EAAErB,SAAS,EAAES,aAAa,CAAC;EAC5B,MAAMmB,yBAAyB,GAAGzC,KAAK,CAACqC,OAAO,CAAC,MAAM;IACpD,IAAI,OAAOhB,iBAAiB,KAAK,QAAQ,EAAE;MACzC,OAAOA,iBAAiB;IAC1B;IACA,IAAI,OAAO,CAACqB,IAAI,CAACrB,iBAAiB,CAAC,EAAE;MACnC,OAAOsB,MAAM,CAACtB,iBAAiB,CAAC;IAClC;IACA,OAAOA,iBAAiB;EAC1B,CAAC,EAAE,CAACA,iBAAiB,CAAC,CAAC;EACvB,MAAMuB,UAAU,GAAG;IACjBC,iBAAiB,EAAEP,cAAc,GAAGG,yBAAyB,GAAGK,SAAS;IACzEC,eAAe,EAAER,YAAY,GAAGE,yBAAyB,GAAGK;EAC9D,CAAC;EACD;EACA,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGjD,aAAa,CAAC,SAAS,CAAC;IACxC8C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAAC,CAAC5B,QAAQ,IAAIJ,IAAI,KAAK,UAAU,EAAE,OAAO,EAAE,4CAA4C,CAAC,GAAG,KAAK,CAAC;EACnJ;EACA,OAAOW,UAAU,CAAC,aAAa9B,KAAK,CAACoD,aAAa,CAAC,KAAK,EAAE7D,MAAM,CAAC8D,MAAM,CAAC;IACtExC,SAAS,EAAE2B,WAAW;IACtBzB,KAAK,EAAExB,MAAM,CAAC8D,MAAM,CAAC9D,MAAM,CAAC8D,MAAM,CAAC,CAAC,CAAC,EAAErC,YAAY,CAAC,EAAED,KAAK;EAC7D,CAAC,EAAEc,SAAS,EAAE;IACZyB,IAAI,EAAE;EACR,CAAC,CAAC,EAAE/B,QAAQ,IAAIJ,IAAI,KAAK,UAAU,KAAK,aAAanB,KAAK,CAACoD,aAAa,CAAC,MAAM,EAAE;IAC/EvC,SAAS,EAAE,GAAGI,SAAS,aAAa;IACpCF,KAAK,EAAE6B;EACT,CAAC,EAAErB,QAAQ,CAAC,CAAC,CAAC,CAAC;AACjB,CAAC;AACD,IAAIyB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCzC,OAAO,CAAC8C,WAAW,GAAG,SAAS;AACjC;AACA,eAAe9C,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}