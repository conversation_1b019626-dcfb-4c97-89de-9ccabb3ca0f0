{"ast": null, "code": "// Style as confirm component\nimport { unit } from '@ant-design/cssinjs';\nimport { prepareComponentToken, prepareToken } from '.';\nimport { clearFix } from '../../style';\nimport { genSubStyleComponent } from '../../theme/internal';\n// ============================= Confirm ==============================\nconst genModalConfirmStyle = token => {\n  const {\n    componentCls,\n    titleFontSize,\n    titleLineHeight,\n    modalConfirmIconSize,\n    fontSize,\n    lineHeight,\n    modalTitleHeight,\n    fontHeight,\n    confirmBodyPadding\n  } = token;\n  const confirmComponentCls = `${componentCls}-confirm`;\n  return {\n    [confirmComponentCls]: {\n      '&-rtl': {\n        direction: 'rtl'\n      },\n      [`${token.antCls}-modal-header`]: {\n        display: 'none'\n      },\n      [`${confirmComponentCls}-body-wrapper`]: Object.assign({}, clearFix()),\n      [`&${componentCls} ${componentCls}-body`]: {\n        padding: confirmBodyPadding\n      },\n      // ====================== Body ======================\n      [`${confirmComponentCls}-body`]: {\n        display: 'flex',\n        flexWrap: 'nowrap',\n        alignItems: 'start',\n        [`> ${token.iconCls}`]: {\n          flex: 'none',\n          fontSize: modalConfirmIconSize,\n          marginInlineEnd: token.confirmIconMarginInlineEnd,\n          marginTop: token.calc(token.calc(fontHeight).sub(modalConfirmIconSize).equal()).div(2).equal()\n        },\n        [`&-has-title > ${token.iconCls}`]: {\n          marginTop: token.calc(token.calc(modalTitleHeight).sub(modalConfirmIconSize).equal()).div(2).equal()\n        }\n      },\n      [`${confirmComponentCls}-paragraph`]: {\n        display: 'flex',\n        flexDirection: 'column',\n        flex: 'auto',\n        rowGap: token.marginXS,\n        // https://github.com/ant-design/ant-design/issues/51912\n        maxWidth: `calc(100% - ${unit(token.marginSM)})`\n      },\n      // https://github.com/ant-design/ant-design/issues/48159\n      [`${token.iconCls} + ${confirmComponentCls}-paragraph`]: {\n        maxWidth: `calc(100% - ${unit(token.calc(token.modalConfirmIconSize).add(token.marginSM).equal())})`\n      },\n      [`${confirmComponentCls}-title`]: {\n        color: token.colorTextHeading,\n        fontWeight: token.fontWeightStrong,\n        fontSize: titleFontSize,\n        lineHeight: titleLineHeight\n      },\n      [`${confirmComponentCls}-content`]: {\n        color: token.colorText,\n        fontSize,\n        lineHeight\n      },\n      // ===================== Footer =====================\n      [`${confirmComponentCls}-btns`]: {\n        textAlign: 'end',\n        marginTop: token.confirmBtnsMarginTop,\n        [`${token.antCls}-btn + ${token.antCls}-btn`]: {\n          marginBottom: 0,\n          marginInlineStart: token.marginXS\n        }\n      }\n    },\n    [`${confirmComponentCls}-error ${confirmComponentCls}-body > ${token.iconCls}`]: {\n      color: token.colorError\n    },\n    [`${confirmComponentCls}-warning ${confirmComponentCls}-body > ${token.iconCls},\n        ${confirmComponentCls}-confirm ${confirmComponentCls}-body > ${token.iconCls}`]: {\n      color: token.colorWarning\n    },\n    [`${confirmComponentCls}-info ${confirmComponentCls}-body > ${token.iconCls}`]: {\n      color: token.colorInfo\n    },\n    [`${confirmComponentCls}-success ${confirmComponentCls}-body > ${token.iconCls}`]: {\n      color: token.colorSuccess\n    }\n  };\n};\n// ============================== Export ==============================\nexport default genSubStyleComponent(['Modal', 'confirm'], token => {\n  const modalToken = prepareToken(token);\n  return [genModalConfirmStyle(modalToken)];\n}, prepareComponentToken, {\n  // confirm is weak than modal since no conflict here\n  order: -1000\n});", "map": {"version": 3, "names": ["unit", "prepareComponentToken", "prepareToken", "clearFix", "genSubStyleComponent", "genModalConfirmStyle", "token", "componentCls", "titleFontSize", "titleLineHeight", "modalConfirmIconSize", "fontSize", "lineHeight", "modalTitleHeight", "fontHeight", "confirmBodyPadding", "confirmComponentCls", "direction", "antCls", "display", "Object", "assign", "padding", "flexWrap", "alignItems", "iconCls", "flex", "marginInlineEnd", "confirmIconMarginInlineEnd", "marginTop", "calc", "sub", "equal", "div", "flexDirection", "rowGap", "marginXS", "max<PERSON><PERSON><PERSON>", "marginSM", "add", "color", "colorTextHeading", "fontWeight", "fontWeightStrong", "colorText", "textAlign", "confirmBtnsMarginTop", "marginBottom", "marginInlineStart", "colorError", "colorWarning", "colorInfo", "colorSuccess", "modalToken", "order"], "sources": ["/Users/<USER>/Desktop/code/system/frontend/node_modules/antd/es/modal/style/confirm.js"], "sourcesContent": ["// Style as confirm component\nimport { unit } from '@ant-design/cssinjs';\nimport { prepareComponentToken, prepareToken } from '.';\nimport { clearFix } from '../../style';\nimport { genSubStyleComponent } from '../../theme/internal';\n// ============================= Confirm ==============================\nconst genModalConfirmStyle = token => {\n  const {\n    componentCls,\n    titleFontSize,\n    titleLineHeight,\n    modalConfirmIconSize,\n    fontSize,\n    lineHeight,\n    modalTitleHeight,\n    fontHeight,\n    confirmBodyPadding\n  } = token;\n  const confirmComponentCls = `${componentCls}-confirm`;\n  return {\n    [confirmComponentCls]: {\n      '&-rtl': {\n        direction: 'rtl'\n      },\n      [`${token.antCls}-modal-header`]: {\n        display: 'none'\n      },\n      [`${confirmComponentCls}-body-wrapper`]: Object.assign({}, clearFix()),\n      [`&${componentCls} ${componentCls}-body`]: {\n        padding: confirmBodyPadding\n      },\n      // ====================== Body ======================\n      [`${confirmComponentCls}-body`]: {\n        display: 'flex',\n        flexWrap: 'nowrap',\n        alignItems: 'start',\n        [`> ${token.iconCls}`]: {\n          flex: 'none',\n          fontSize: modalConfirmIconSize,\n          marginInlineEnd: token.confirmIconMarginInlineEnd,\n          marginTop: token.calc(token.calc(fontHeight).sub(modalConfirmIconSize).equal()).div(2).equal()\n        },\n        [`&-has-title > ${token.iconCls}`]: {\n          marginTop: token.calc(token.calc(modalTitleHeight).sub(modalConfirmIconSize).equal()).div(2).equal()\n        }\n      },\n      [`${confirmComponentCls}-paragraph`]: {\n        display: 'flex',\n        flexDirection: 'column',\n        flex: 'auto',\n        rowGap: token.marginXS,\n        // https://github.com/ant-design/ant-design/issues/51912\n        maxWidth: `calc(100% - ${unit(token.marginSM)})`\n      },\n      // https://github.com/ant-design/ant-design/issues/48159\n      [`${token.iconCls} + ${confirmComponentCls}-paragraph`]: {\n        maxWidth: `calc(100% - ${unit(token.calc(token.modalConfirmIconSize).add(token.marginSM).equal())})`\n      },\n      [`${confirmComponentCls}-title`]: {\n        color: token.colorTextHeading,\n        fontWeight: token.fontWeightStrong,\n        fontSize: titleFontSize,\n        lineHeight: titleLineHeight\n      },\n      [`${confirmComponentCls}-content`]: {\n        color: token.colorText,\n        fontSize,\n        lineHeight\n      },\n      // ===================== Footer =====================\n      [`${confirmComponentCls}-btns`]: {\n        textAlign: 'end',\n        marginTop: token.confirmBtnsMarginTop,\n        [`${token.antCls}-btn + ${token.antCls}-btn`]: {\n          marginBottom: 0,\n          marginInlineStart: token.marginXS\n        }\n      }\n    },\n    [`${confirmComponentCls}-error ${confirmComponentCls}-body > ${token.iconCls}`]: {\n      color: token.colorError\n    },\n    [`${confirmComponentCls}-warning ${confirmComponentCls}-body > ${token.iconCls},\n        ${confirmComponentCls}-confirm ${confirmComponentCls}-body > ${token.iconCls}`]: {\n      color: token.colorWarning\n    },\n    [`${confirmComponentCls}-info ${confirmComponentCls}-body > ${token.iconCls}`]: {\n      color: token.colorInfo\n    },\n    [`${confirmComponentCls}-success ${confirmComponentCls}-body > ${token.iconCls}`]: {\n      color: token.colorSuccess\n    }\n  };\n};\n// ============================== Export ==============================\nexport default genSubStyleComponent(['Modal', 'confirm'], token => {\n  const modalToken = prepareToken(token);\n  return [genModalConfirmStyle(modalToken)];\n}, prepareComponentToken, {\n  // confirm is weak than modal since no conflict here\n  order: -1000\n});"], "mappings": "AAAA;AACA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,qBAAqB,EAAEC,YAAY,QAAQ,GAAG;AACvD,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,oBAAoB,QAAQ,sBAAsB;AAC3D;AACA,MAAMC,oBAAoB,GAAGC,KAAK,IAAI;EACpC,MAAM;IACJC,YAAY;IACZC,aAAa;IACbC,eAAe;IACfC,oBAAoB;IACpBC,QAAQ;IACRC,UAAU;IACVC,gBAAgB;IAChBC,UAAU;IACVC;EACF,CAAC,GAAGT,KAAK;EACT,MAAMU,mBAAmB,GAAG,GAAGT,YAAY,UAAU;EACrD,OAAO;IACL,CAACS,mBAAmB,GAAG;MACrB,OAAO,EAAE;QACPC,SAAS,EAAE;MACb,CAAC;MACD,CAAC,GAAGX,KAAK,CAACY,MAAM,eAAe,GAAG;QAChCC,OAAO,EAAE;MACX,CAAC;MACD,CAAC,GAAGH,mBAAmB,eAAe,GAAGI,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAElB,QAAQ,CAAC,CAAC,CAAC;MACtE,CAAC,IAAII,YAAY,IAAIA,YAAY,OAAO,GAAG;QACzCe,OAAO,EAAEP;MACX,CAAC;MACD;MACA,CAAC,GAAGC,mBAAmB,OAAO,GAAG;QAC/BG,OAAO,EAAE,MAAM;QACfI,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE,OAAO;QACnB,CAAC,KAAKlB,KAAK,CAACmB,OAAO,EAAE,GAAG;UACtBC,IAAI,EAAE,MAAM;UACZf,QAAQ,EAAED,oBAAoB;UAC9BiB,eAAe,EAAErB,KAAK,CAACsB,0BAA0B;UACjDC,SAAS,EAAEvB,KAAK,CAACwB,IAAI,CAACxB,KAAK,CAACwB,IAAI,CAAChB,UAAU,CAAC,CAACiB,GAAG,CAACrB,oBAAoB,CAAC,CAACsB,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC;QAC/F,CAAC;QACD,CAAC,iBAAiB1B,KAAK,CAACmB,OAAO,EAAE,GAAG;UAClCI,SAAS,EAAEvB,KAAK,CAACwB,IAAI,CAACxB,KAAK,CAACwB,IAAI,CAACjB,gBAAgB,CAAC,CAACkB,GAAG,CAACrB,oBAAoB,CAAC,CAACsB,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC;QACrG;MACF,CAAC;MACD,CAAC,GAAGhB,mBAAmB,YAAY,GAAG;QACpCG,OAAO,EAAE,MAAM;QACfe,aAAa,EAAE,QAAQ;QACvBR,IAAI,EAAE,MAAM;QACZS,MAAM,EAAE7B,KAAK,CAAC8B,QAAQ;QACtB;QACAC,QAAQ,EAAE,eAAerC,IAAI,CAACM,KAAK,CAACgC,QAAQ,CAAC;MAC/C,CAAC;MACD;MACA,CAAC,GAAGhC,KAAK,CAACmB,OAAO,MAAMT,mBAAmB,YAAY,GAAG;QACvDqB,QAAQ,EAAE,eAAerC,IAAI,CAACM,KAAK,CAACwB,IAAI,CAACxB,KAAK,CAACI,oBAAoB,CAAC,CAAC6B,GAAG,CAACjC,KAAK,CAACgC,QAAQ,CAAC,CAACN,KAAK,CAAC,CAAC,CAAC;MACnG,CAAC;MACD,CAAC,GAAGhB,mBAAmB,QAAQ,GAAG;QAChCwB,KAAK,EAAElC,KAAK,CAACmC,gBAAgB;QAC7BC,UAAU,EAAEpC,KAAK,CAACqC,gBAAgB;QAClChC,QAAQ,EAAEH,aAAa;QACvBI,UAAU,EAAEH;MACd,CAAC;MACD,CAAC,GAAGO,mBAAmB,UAAU,GAAG;QAClCwB,KAAK,EAAElC,KAAK,CAACsC,SAAS;QACtBjC,QAAQ;QACRC;MACF,CAAC;MACD;MACA,CAAC,GAAGI,mBAAmB,OAAO,GAAG;QAC/B6B,SAAS,EAAE,KAAK;QAChBhB,SAAS,EAAEvB,KAAK,CAACwC,oBAAoB;QACrC,CAAC,GAAGxC,KAAK,CAACY,MAAM,UAAUZ,KAAK,CAACY,MAAM,MAAM,GAAG;UAC7C6B,YAAY,EAAE,CAAC;UACfC,iBAAiB,EAAE1C,KAAK,CAAC8B;QAC3B;MACF;IACF,CAAC;IACD,CAAC,GAAGpB,mBAAmB,UAAUA,mBAAmB,WAAWV,KAAK,CAACmB,OAAO,EAAE,GAAG;MAC/Ee,KAAK,EAAElC,KAAK,CAAC2C;IACf,CAAC;IACD,CAAC,GAAGjC,mBAAmB,YAAYA,mBAAmB,WAAWV,KAAK,CAACmB,OAAO;AAClF,UAAUT,mBAAmB,YAAYA,mBAAmB,WAAWV,KAAK,CAACmB,OAAO,EAAE,GAAG;MACnFe,KAAK,EAAElC,KAAK,CAAC4C;IACf,CAAC;IACD,CAAC,GAAGlC,mBAAmB,SAASA,mBAAmB,WAAWV,KAAK,CAACmB,OAAO,EAAE,GAAG;MAC9Ee,KAAK,EAAElC,KAAK,CAAC6C;IACf,CAAC;IACD,CAAC,GAAGnC,mBAAmB,YAAYA,mBAAmB,WAAWV,KAAK,CAACmB,OAAO,EAAE,GAAG;MACjFe,KAAK,EAAElC,KAAK,CAAC8C;IACf;EACF,CAAC;AACH,CAAC;AACD;AACA,eAAehD,oBAAoB,CAAC,CAAC,OAAO,EAAE,SAAS,CAAC,EAAEE,KAAK,IAAI;EACjE,MAAM+C,UAAU,GAAGnD,YAAY,CAACI,KAAK,CAAC;EACtC,OAAO,CAACD,oBAAoB,CAACgD,UAAU,CAAC,CAAC;AAC3C,CAAC,EAAEpD,qBAAqB,EAAE;EACxB;EACAqD,KAAK,EAAE,CAAC;AACV,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}