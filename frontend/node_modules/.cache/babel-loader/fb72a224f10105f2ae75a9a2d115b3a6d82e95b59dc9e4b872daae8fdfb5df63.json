{"ast": null, "code": "var Option = function Option() {\n  return null;\n};\nexport default Option;", "map": {"version": 3, "names": ["Option"], "sources": ["/Users/<USER>/Desktop/code/system/frontend/node_modules/rc-mentions/es/Option.js"], "sourcesContent": ["var Option = function Option() {\n  return null;\n};\nexport default Option;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,OAAO,IAAI;AACb,CAAC;AACD,eAAeA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}