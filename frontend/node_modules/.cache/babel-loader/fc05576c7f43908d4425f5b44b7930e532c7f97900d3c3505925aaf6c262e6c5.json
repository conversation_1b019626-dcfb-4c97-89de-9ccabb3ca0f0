{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport GiftTwoToneSvg from \"@ant-design/icons-svg/es/asn/GiftTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar GiftTwoTone = function GiftTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: GiftTwoToneSvg\n  }));\n};\n\n/**![gift](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTU0NiAzNzhoMjk4djEwNEg1NDZ6TTIyOCA1NTBoMjUwdjMwOEgyMjh6bS00OC0xNzJoMjk4djEwNEgxODB6bTM2NiAxNzJoMjUwdjMwOEg1NDZ6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik04ODAgMzEwSDczMi40YzEzLjYtMjEuNCAyMS42LTQ2LjggMjEuNi03NCAwLTc2LjEtNjEuOS0xMzgtMTM4LTEzOC00MS40IDAtNzguNyAxOC40LTEwNCA0Ny40LTI1LjMtMjktNjIuNi00Ny40LTEwNC00Ny40LTc2LjEgMC0xMzggNjEuOS0xMzggMTM4IDAgMjcuMiA3LjkgNTIuNiAyMS42IDc0SDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MjAwYzAgNC40IDMuNiA4IDggOGg0MHYzNDRjMCAxNy43IDE0LjMgMzIgMzIgMzJoNjQwYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjU1MGg0MGM0LjQgMCA4LTMuNiA4LThWMzQyYzAtMTcuNy0xNC4zLTMyLTMyLTMyek00NzggODU4SDIyOFY1NTBoMjUwdjMwOHptMC0zNzZIMTgwVjM3OGgyOTh2MTA0em0wLTE3NmgtNzBjLTM4LjYgMC03MC0zMS40LTcwLTcwczMxLjQtNzAgNzAtNzAgNzAgMzEuNCA3MCA3MHY3MHptNjgtNzBjMC0zOC42IDMxLjQtNzAgNzAtNzBzNzAgMzEuNCA3MCA3MC0zMS40IDcwLTcwIDcwaC03MHYtNzB6bTI1MCA2MjJINTQ2VjU1MGgyNTB2MzA4em00OC0zNzZINTQ2VjM3OGgyOTh2MTA0eiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(GiftTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'GiftTwoTone';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "GiftTwoToneSvg", "AntdIcon", "GiftTwoTone", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Desktop/code/system/frontend/node_modules/@ant-design/icons/es/icons/GiftTwoTone.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport GiftTwoToneSvg from \"@ant-design/icons-svg/es/asn/GiftTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar GiftTwoTone = function GiftTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: GiftTwoToneSvg\n  }));\n};\n\n/**![gift](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTU0NiAzNzhoMjk4djEwNEg1NDZ6TTIyOCA1NTBoMjUwdjMwOEgyMjh6bS00OC0xNzJoMjk4djEwNEgxODB6bTM2NiAxNzJoMjUwdjMwOEg1NDZ6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik04ODAgMzEwSDczMi40YzEzLjYtMjEuNCAyMS42LTQ2LjggMjEuNi03NCAwLTc2LjEtNjEuOS0xMzgtMTM4LTEzOC00MS40IDAtNzguNyAxOC40LTEwNCA0Ny40LTI1LjMtMjktNjIuNi00Ny40LTEwNC00Ny40LTc2LjEgMC0xMzggNjEuOS0xMzggMTM4IDAgMjcuMiA3LjkgNTIuNiAyMS42IDc0SDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MjAwYzAgNC40IDMuNiA4IDggOGg0MHYzNDRjMCAxNy43IDE0LjMgMzIgMzIgMzJoNjQwYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjU1MGg0MGM0LjQgMCA4LTMuNiA4LThWMzQyYzAtMTcuNy0xNC4zLTMyLTMyLTMyek00NzggODU4SDIyOFY1NTBoMjUwdjMwOHptMC0zNzZIMTgwVjM3OGgyOTh2MTA0em0wLTE3NmgtNzBjLTM4LjYgMC03MC0zMS40LTcwLTcwczMxLjQtNzAgNzAtNzAgNzAgMzEuNCA3MCA3MHY3MHptNjgtNzBjMC0zOC42IDMxLjQtNzAgNzAtNzBzNzAgMzEuNCA3MCA3MC0zMS40IDcwLTcwIDcwaC03MHYtNzB6bTI1MCA2MjJINTQ2VjU1MGgyNTB2MzA4em00OC0zNzZINTQ2VjM3OGgyOTh2MTA0eiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(GiftTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'GiftTwoTone';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,WAAW,CAAC;AACxD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,aAAa;AACrC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}