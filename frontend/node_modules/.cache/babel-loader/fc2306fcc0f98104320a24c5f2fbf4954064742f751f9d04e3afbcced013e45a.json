{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React, { memo } from 'react';\nimport FileTextOutlined from \"@ant-design/icons/es/icons/FileTextOutlined\";\nimport classNames from 'classnames';\nconst FloatButtonContent = props => {\n  const {\n      icon,\n      description,\n      prefixCls,\n      className\n    } = props,\n    rest = __rest(props, [\"icon\", \"description\", \"prefixCls\", \"className\"]);\n  const defaultElement = /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-icon`\n  }, /*#__PURE__*/React.createElement(FileTextOutlined, null));\n  return /*#__PURE__*/React.createElement(\"div\", Object.assign({}, rest, {\n    className: classNames(className, `${prefixCls}-content`)\n  }), icon || description ? (/*#__PURE__*/React.createElement(React.Fragment, null, icon && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-icon`\n  }, icon), description && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-description`\n  }, description))) : defaultElement);\n};\nexport default /*#__PURE__*/memo(FloatButtonContent);", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "memo", "FileTextOutlined", "classNames", "FloatButtonContent", "props", "icon", "description", "prefixCls", "className", "rest", "defaultElement", "createElement", "assign", "Fragment"], "sources": ["/Users/<USER>/Desktop/code/system/frontend/node_modules/antd/es/float-button/FloatButtonContent.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React, { memo } from 'react';\nimport FileTextOutlined from \"@ant-design/icons/es/icons/FileTextOutlined\";\nimport classNames from 'classnames';\nconst FloatButtonContent = props => {\n  const {\n      icon,\n      description,\n      prefixCls,\n      className\n    } = props,\n    rest = __rest(props, [\"icon\", \"description\", \"prefixCls\", \"className\"]);\n  const defaultElement = /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-icon`\n  }, /*#__PURE__*/React.createElement(FileTextOutlined, null));\n  return /*#__PURE__*/React.createElement(\"div\", Object.assign({}, rest, {\n    className: classNames(className, `${prefixCls}-content`)\n  }), icon || description ? (/*#__PURE__*/React.createElement(React.Fragment, null, icon && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-icon`\n  }, icon), description && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-description`\n  }, description))) : defaultElement);\n};\nexport default /*#__PURE__*/memo(FloatButtonContent);"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,KAAK,IAAIC,IAAI,QAAQ,OAAO;AACnC,OAAOC,gBAAgB,MAAM,6CAA6C;AAC1E,OAAOC,UAAU,MAAM,YAAY;AACnC,MAAMC,kBAAkB,GAAGC,KAAK,IAAI;EAClC,MAAM;MACFC,IAAI;MACJC,WAAW;MACXC,SAAS;MACTC;IACF,CAAC,GAAGJ,KAAK;IACTK,IAAI,GAAGxB,MAAM,CAACmB,KAAK,EAAE,CAAC,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;EACzE,MAAMM,cAAc,GAAG,aAAaX,KAAK,CAACY,aAAa,CAAC,KAAK,EAAE;IAC7DH,SAAS,EAAE,GAAGD,SAAS;EACzB,CAAC,EAAE,aAAaR,KAAK,CAACY,aAAa,CAACV,gBAAgB,EAAE,IAAI,CAAC,CAAC;EAC5D,OAAO,aAAaF,KAAK,CAACY,aAAa,CAAC,KAAK,EAAErB,MAAM,CAACsB,MAAM,CAAC,CAAC,CAAC,EAAEH,IAAI,EAAE;IACrED,SAAS,EAAEN,UAAU,CAACM,SAAS,EAAE,GAAGD,SAAS,UAAU;EACzD,CAAC,CAAC,EAAEF,IAAI,IAAIC,WAAW,IAAI,aAAaP,KAAK,CAACY,aAAa,CAACZ,KAAK,CAACc,QAAQ,EAAE,IAAI,EAAER,IAAI,IAAI,aAAaN,KAAK,CAACY,aAAa,CAAC,KAAK,EAAE;IAChIH,SAAS,EAAE,GAAGD,SAAS;EACzB,CAAC,EAAEF,IAAI,CAAC,EAAEC,WAAW,IAAI,aAAaP,KAAK,CAACY,aAAa,CAAC,KAAK,EAAE;IAC/DH,SAAS,EAAE,GAAGD,SAAS;EACzB,CAAC,EAAED,WAAW,CAAC,CAAC,IAAII,cAAc,CAAC;AACrC,CAAC;AACD,eAAe,aAAaV,IAAI,CAACG,kBAAkB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}