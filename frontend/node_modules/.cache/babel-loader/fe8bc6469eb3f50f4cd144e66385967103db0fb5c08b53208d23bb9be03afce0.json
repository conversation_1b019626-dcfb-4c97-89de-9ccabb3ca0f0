{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"className\", \"style\", \"min\", \"max\", \"step\", \"defaultValue\", \"value\", \"disabled\", \"readOnly\", \"upHandler\", \"downHandler\", \"keyboard\", \"changeOnWheel\", \"controls\", \"classNames\", \"stringMode\", \"parser\", \"formatter\", \"precision\", \"decimalSeparator\", \"onChange\", \"onInput\", \"onPressEnter\", \"onStep\", \"changeOnBlur\", \"domRef\"],\n  _excluded2 = [\"disabled\", \"style\", \"prefixCls\", \"value\", \"prefix\", \"suffix\", \"addonBefore\", \"addonAfter\", \"className\", \"classNames\"];\nimport getMiniDecimal, { getNumberPrecision, num2str, toFixed, validateNumber } from '@rc-component/mini-decimal';\nimport clsx from 'classnames';\nimport { BaseInput } from 'rc-input';\nimport { useLayoutUpdateEffect } from \"rc-util/es/hooks/useLayoutEffect\";\nimport proxyObject from \"rc-util/es/proxyObject\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport useCursor from \"./hooks/useCursor\";\nimport StepHandler from \"./StepHandler\";\nimport { getDecupleSteps } from \"./utils/numberUtil\";\nimport { triggerFocus } from \"rc-input/es/utils/commonUtils\";\nimport useFrame from \"./hooks/useFrame\";\n/**\n * We support `stringMode` which need handle correct type when user call in onChange\n * format max or min value\n * 1. if isInvalid return null\n * 2. if precision is undefined, return decimal\n * 3. format with precision\n *    I. if max > 0, round down with precision. Example: max= 3.5, precision=0  afterFormat: 3\n *    II. if max < 0, round up with precision. Example: max= -3.5, precision=0  afterFormat: -4\n *    III. if min > 0, round up with precision. Example: min= 3.5, precision=0  afterFormat: 4\n *    IV. if min < 0, round down with precision. Example: max= -3.5, precision=0  afterFormat: -3\n */\nvar getDecimalValue = function getDecimalValue(stringMode, decimalValue) {\n  if (stringMode || decimalValue.isEmpty()) {\n    return decimalValue.toString();\n  }\n  return decimalValue.toNumber();\n};\nvar getDecimalIfValidate = function getDecimalIfValidate(value) {\n  var decimal = getMiniDecimal(value);\n  return decimal.isInvalidate() ? null : decimal;\n};\nvar InternalInputNumber = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    min = props.min,\n    max = props.max,\n    _props$step = props.step,\n    step = _props$step === void 0 ? 1 : _props$step,\n    defaultValue = props.defaultValue,\n    value = props.value,\n    disabled = props.disabled,\n    readOnly = props.readOnly,\n    upHandler = props.upHandler,\n    downHandler = props.downHandler,\n    keyboard = props.keyboard,\n    _props$changeOnWheel = props.changeOnWheel,\n    changeOnWheel = _props$changeOnWheel === void 0 ? false : _props$changeOnWheel,\n    _props$controls = props.controls,\n    controls = _props$controls === void 0 ? true : _props$controls,\n    classNames = props.classNames,\n    stringMode = props.stringMode,\n    parser = props.parser,\n    formatter = props.formatter,\n    precision = props.precision,\n    decimalSeparator = props.decimalSeparator,\n    onChange = props.onChange,\n    onInput = props.onInput,\n    onPressEnter = props.onPressEnter,\n    onStep = props.onStep,\n    _props$changeOnBlur = props.changeOnBlur,\n    changeOnBlur = _props$changeOnBlur === void 0 ? true : _props$changeOnBlur,\n    domRef = props.domRef,\n    inputProps = _objectWithoutProperties(props, _excluded);\n  var inputClassName = \"\".concat(prefixCls, \"-input\");\n  var inputRef = React.useRef(null);\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    focus = _React$useState2[0],\n    setFocus = _React$useState2[1];\n  var userTypingRef = React.useRef(false);\n  var compositionRef = React.useRef(false);\n  var shiftKeyRef = React.useRef(false);\n\n  // ============================ Value =============================\n  // Real value control\n  var _React$useState3 = React.useState(function () {\n      return getMiniDecimal(value !== null && value !== void 0 ? value : defaultValue);\n    }),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    decimalValue = _React$useState4[0],\n    setDecimalValue = _React$useState4[1];\n  function setUncontrolledDecimalValue(newDecimal) {\n    if (value === undefined) {\n      setDecimalValue(newDecimal);\n    }\n  }\n\n  // ====================== Parser & Formatter ======================\n  /**\n   * `precision` is used for formatter & onChange.\n   * It will auto generate by `value` & `step`.\n   * But it will not block user typing.\n   *\n   * Note: Auto generate `precision` is used for legacy logic.\n   * We should remove this since we already support high precision with BigInt.\n   *\n   * @param number  Provide which number should calculate precision\n   * @param userTyping  Change by user typing\n   */\n  var getPrecision = React.useCallback(function (numStr, userTyping) {\n    if (userTyping) {\n      return undefined;\n    }\n    if (precision >= 0) {\n      return precision;\n    }\n    return Math.max(getNumberPrecision(numStr), getNumberPrecision(step));\n  }, [precision, step]);\n\n  // >>> Parser\n  var mergedParser = React.useCallback(function (num) {\n    var numStr = String(num);\n    if (parser) {\n      return parser(numStr);\n    }\n    var parsedStr = numStr;\n    if (decimalSeparator) {\n      parsedStr = parsedStr.replace(decimalSeparator, '.');\n    }\n\n    // [Legacy] We still support auto convert `$ 123,456` to `123456`\n    return parsedStr.replace(/[^\\w.-]+/g, '');\n  }, [parser, decimalSeparator]);\n\n  // >>> Formatter\n  var inputValueRef = React.useRef('');\n  var mergedFormatter = React.useCallback(function (number, userTyping) {\n    if (formatter) {\n      return formatter(number, {\n        userTyping: userTyping,\n        input: String(inputValueRef.current)\n      });\n    }\n    var str = typeof number === 'number' ? num2str(number) : number;\n\n    // User typing will not auto format with precision directly\n    if (!userTyping) {\n      var mergedPrecision = getPrecision(str, userTyping);\n      if (validateNumber(str) && (decimalSeparator || mergedPrecision >= 0)) {\n        // Separator\n        var separatorStr = decimalSeparator || '.';\n        str = toFixed(str, separatorStr, mergedPrecision);\n      }\n    }\n    return str;\n  }, [formatter, getPrecision, decimalSeparator]);\n\n  // ========================== InputValue ==========================\n  /**\n   * Input text value control\n   *\n   * User can not update input content directly. It updates with follow rules by priority:\n   *  1. controlled `value` changed\n   *    * [SPECIAL] Typing like `1.` should not immediately convert to `1`\n   *  2. User typing with format (not precision)\n   *  3. Blur or Enter trigger revalidate\n   */\n  var _React$useState5 = React.useState(function () {\n      var initValue = defaultValue !== null && defaultValue !== void 0 ? defaultValue : value;\n      if (decimalValue.isInvalidate() && ['string', 'number'].includes(_typeof(initValue))) {\n        return Number.isNaN(initValue) ? '' : initValue;\n      }\n      return mergedFormatter(decimalValue.toString(), false);\n    }),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    inputValue = _React$useState6[0],\n    setInternalInputValue = _React$useState6[1];\n  inputValueRef.current = inputValue;\n\n  // Should always be string\n  function setInputValue(newValue, userTyping) {\n    setInternalInputValue(mergedFormatter(\n    // Invalidate number is sometime passed by external control, we should let it go\n    // Otherwise is controlled by internal interactive logic which check by userTyping\n    // You can ref 'show limited value when input is not focused' test for more info.\n    newValue.isInvalidate() ? newValue.toString(false) : newValue.toString(!userTyping), userTyping));\n  }\n\n  // >>> Max & Min limit\n  var maxDecimal = React.useMemo(function () {\n    return getDecimalIfValidate(max);\n  }, [max, precision]);\n  var minDecimal = React.useMemo(function () {\n    return getDecimalIfValidate(min);\n  }, [min, precision]);\n  var upDisabled = React.useMemo(function () {\n    if (!maxDecimal || !decimalValue || decimalValue.isInvalidate()) {\n      return false;\n    }\n    return maxDecimal.lessEquals(decimalValue);\n  }, [maxDecimal, decimalValue]);\n  var downDisabled = React.useMemo(function () {\n    if (!minDecimal || !decimalValue || decimalValue.isInvalidate()) {\n      return false;\n    }\n    return decimalValue.lessEquals(minDecimal);\n  }, [minDecimal, decimalValue]);\n\n  // Cursor controller\n  var _useCursor = useCursor(inputRef.current, focus),\n    _useCursor2 = _slicedToArray(_useCursor, 2),\n    recordCursor = _useCursor2[0],\n    restoreCursor = _useCursor2[1];\n\n  // ============================= Data =============================\n  /**\n   * Find target value closet within range.\n   * e.g. [11, 28]:\n   *    3  => 11\n   *    23 => 23\n   *    99 => 28\n   */\n  var getRangeValue = function getRangeValue(target) {\n    // target > max\n    if (maxDecimal && !target.lessEquals(maxDecimal)) {\n      return maxDecimal;\n    }\n\n    // target < min\n    if (minDecimal && !minDecimal.lessEquals(target)) {\n      return minDecimal;\n    }\n    return null;\n  };\n\n  /**\n   * Check value is in [min, max] range\n   */\n  var isInRange = function isInRange(target) {\n    return !getRangeValue(target);\n  };\n\n  /**\n   * Trigger `onChange` if value validated and not equals of origin.\n   * Return the value that re-align in range.\n   */\n  var triggerValueUpdate = function triggerValueUpdate(newValue, userTyping) {\n    var updateValue = newValue;\n    var isRangeValidate = isInRange(updateValue) || updateValue.isEmpty();\n\n    // Skip align value when trigger value is empty.\n    // We just trigger onChange(null)\n    // This should not block user typing\n    if (!updateValue.isEmpty() && !userTyping) {\n      // Revert value in range if needed\n      updateValue = getRangeValue(updateValue) || updateValue;\n      isRangeValidate = true;\n    }\n    if (!readOnly && !disabled && isRangeValidate) {\n      var numStr = updateValue.toString();\n      var mergedPrecision = getPrecision(numStr, userTyping);\n      if (mergedPrecision >= 0) {\n        updateValue = getMiniDecimal(toFixed(numStr, '.', mergedPrecision));\n\n        // When to fixed. The value may out of min & max range.\n        // 4 in [0, 3.8] => 3.8 => 4 (toFixed)\n        if (!isInRange(updateValue)) {\n          updateValue = getMiniDecimal(toFixed(numStr, '.', mergedPrecision, true));\n        }\n      }\n\n      // Trigger event\n      if (!updateValue.equals(decimalValue)) {\n        setUncontrolledDecimalValue(updateValue);\n        onChange === null || onChange === void 0 || onChange(updateValue.isEmpty() ? null : getDecimalValue(stringMode, updateValue));\n\n        // Reformat input if value is not controlled\n        if (value === undefined) {\n          setInputValue(updateValue, userTyping);\n        }\n      }\n      return updateValue;\n    }\n    return decimalValue;\n  };\n\n  // ========================== User Input ==========================\n  var onNextPromise = useFrame();\n\n  // >>> Collect input value\n  var collectInputValue = function collectInputValue(inputStr) {\n    recordCursor();\n\n    // Update inputValue in case input can not parse as number\n    // Refresh ref value immediately since it may used by formatter\n    inputValueRef.current = inputStr;\n    setInternalInputValue(inputStr);\n\n    // Parse number\n    if (!compositionRef.current) {\n      var finalValue = mergedParser(inputStr);\n      var finalDecimal = getMiniDecimal(finalValue);\n      if (!finalDecimal.isNaN()) {\n        triggerValueUpdate(finalDecimal, true);\n      }\n    }\n\n    // Trigger onInput later to let user customize value if they want to handle something after onChange\n    onInput === null || onInput === void 0 || onInput(inputStr);\n\n    // optimize for chinese input experience\n    // https://github.com/ant-design/ant-design/issues/8196\n    onNextPromise(function () {\n      var nextInputStr = inputStr;\n      if (!parser) {\n        nextInputStr = inputStr.replace(/。/g, '.');\n      }\n      if (nextInputStr !== inputStr) {\n        collectInputValue(nextInputStr);\n      }\n    });\n  };\n\n  // >>> Composition\n  var onCompositionStart = function onCompositionStart() {\n    compositionRef.current = true;\n  };\n  var onCompositionEnd = function onCompositionEnd() {\n    compositionRef.current = false;\n    collectInputValue(inputRef.current.value);\n  };\n\n  // >>> Input\n  var onInternalInput = function onInternalInput(e) {\n    collectInputValue(e.target.value);\n  };\n\n  // ============================= Step =============================\n  var onInternalStep = function onInternalStep(up) {\n    var _inputRef$current;\n    // Ignore step since out of range\n    if (up && upDisabled || !up && downDisabled) {\n      return;\n    }\n\n    // Clear typing status since it may be caused by up & down key.\n    // We should sync with input value.\n    userTypingRef.current = false;\n    var stepDecimal = getMiniDecimal(shiftKeyRef.current ? getDecupleSteps(step) : step);\n    if (!up) {\n      stepDecimal = stepDecimal.negate();\n    }\n    var target = (decimalValue || getMiniDecimal(0)).add(stepDecimal.toString());\n    var updatedValue = triggerValueUpdate(target, false);\n    onStep === null || onStep === void 0 || onStep(getDecimalValue(stringMode, updatedValue), {\n      offset: shiftKeyRef.current ? getDecupleSteps(step) : step,\n      type: up ? 'up' : 'down'\n    });\n    (_inputRef$current = inputRef.current) === null || _inputRef$current === void 0 || _inputRef$current.focus();\n  };\n\n  // ============================ Flush =============================\n  /**\n   * Flush current input content to trigger value change & re-formatter input if needed.\n   * This will always flush input value for update.\n   * If it's invalidate, will fallback to last validate value.\n   */\n  var flushInputValue = function flushInputValue(userTyping) {\n    var parsedValue = getMiniDecimal(mergedParser(inputValue));\n    var formatValue;\n    if (!parsedValue.isNaN()) {\n      // Only validate value or empty value can be re-fill to inputValue\n      // Reassign the formatValue within ranged of trigger control\n      formatValue = triggerValueUpdate(parsedValue, userTyping);\n    } else {\n      formatValue = triggerValueUpdate(decimalValue, userTyping);\n    }\n    if (value !== undefined) {\n      // Reset back with controlled value first\n      setInputValue(decimalValue, false);\n    } else if (!formatValue.isNaN()) {\n      // Reset input back since no validate value\n      setInputValue(formatValue, false);\n    }\n  };\n\n  // Solve the issue of the event triggering sequence when entering numbers in chinese input (Safari)\n  var onBeforeInput = function onBeforeInput() {\n    userTypingRef.current = true;\n  };\n  var onKeyDown = function onKeyDown(event) {\n    var key = event.key,\n      shiftKey = event.shiftKey;\n    userTypingRef.current = true;\n    shiftKeyRef.current = shiftKey;\n    if (key === 'Enter') {\n      if (!compositionRef.current) {\n        userTypingRef.current = false;\n      }\n      flushInputValue(false);\n      onPressEnter === null || onPressEnter === void 0 || onPressEnter(event);\n    }\n    if (keyboard === false) {\n      return;\n    }\n\n    // Do step\n    if (!compositionRef.current && ['Up', 'ArrowUp', 'Down', 'ArrowDown'].includes(key)) {\n      onInternalStep(key === 'Up' || key === 'ArrowUp');\n      event.preventDefault();\n    }\n  };\n  var onKeyUp = function onKeyUp() {\n    userTypingRef.current = false;\n    shiftKeyRef.current = false;\n  };\n  React.useEffect(function () {\n    if (changeOnWheel && focus) {\n      var onWheel = function onWheel(event) {\n        // moving mouse wheel rises wheel event with deltaY < 0\n        // scroll value grows from top to bottom, as screen Y coordinate\n        onInternalStep(event.deltaY < 0);\n        event.preventDefault();\n      };\n      var input = inputRef.current;\n      if (input) {\n        // React onWheel is passive and we can't preventDefault() in it.\n        // That's why we should subscribe with DOM listener\n        // https://stackoverflow.com/questions/63663025/react-onwheel-handler-cant-preventdefault-because-its-a-passive-event-listenev\n        input.addEventListener('wheel', onWheel, {\n          passive: false\n        });\n        return function () {\n          return input.removeEventListener('wheel', onWheel);\n        };\n      }\n    }\n  });\n\n  // >>> Focus & Blur\n  var onBlur = function onBlur() {\n    if (changeOnBlur) {\n      flushInputValue(false);\n    }\n    setFocus(false);\n    userTypingRef.current = false;\n  };\n\n  // ========================== Controlled ==========================\n  // Input by precision & formatter\n  useLayoutUpdateEffect(function () {\n    if (!decimalValue.isInvalidate()) {\n      setInputValue(decimalValue, false);\n    }\n  }, [precision, formatter]);\n\n  // Input by value\n  useLayoutUpdateEffect(function () {\n    var newValue = getMiniDecimal(value);\n    setDecimalValue(newValue);\n    var currentParsedValue = getMiniDecimal(mergedParser(inputValue));\n\n    // When user typing from `1.2` to `1.`, we should not convert to `1` immediately.\n    // But let it go if user set `formatter`\n    if (!newValue.equals(currentParsedValue) || !userTypingRef.current || formatter) {\n      // Update value as effect\n      setInputValue(newValue, userTypingRef.current);\n    }\n  }, [value]);\n\n  // ============================ Cursor ============================\n  useLayoutUpdateEffect(function () {\n    if (formatter) {\n      restoreCursor();\n    }\n  }, [inputValue]);\n\n  // ============================ Render ============================\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: domRef,\n    className: clsx(prefixCls, className, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-focused\"), focus), \"\".concat(prefixCls, \"-disabled\"), disabled), \"\".concat(prefixCls, \"-readonly\"), readOnly), \"\".concat(prefixCls, \"-not-a-number\"), decimalValue.isNaN()), \"\".concat(prefixCls, \"-out-of-range\"), !decimalValue.isInvalidate() && !isInRange(decimalValue))),\n    style: style,\n    onFocus: function onFocus() {\n      setFocus(true);\n    },\n    onBlur: onBlur,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp,\n    onCompositionStart: onCompositionStart,\n    onCompositionEnd: onCompositionEnd,\n    onBeforeInput: onBeforeInput\n  }, controls && /*#__PURE__*/React.createElement(StepHandler, {\n    prefixCls: prefixCls,\n    upNode: upHandler,\n    downNode: downHandler,\n    upDisabled: upDisabled,\n    downDisabled: downDisabled,\n    onStep: onInternalStep\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(inputClassName, \"-wrap\")\n  }, /*#__PURE__*/React.createElement(\"input\", _extends({\n    autoComplete: \"off\",\n    role: \"spinbutton\",\n    \"aria-valuemin\": min,\n    \"aria-valuemax\": max,\n    \"aria-valuenow\": decimalValue.isInvalidate() ? null : decimalValue.toString(),\n    step: step\n  }, inputProps, {\n    ref: composeRef(inputRef, ref),\n    className: inputClassName,\n    value: inputValue,\n    onChange: onInternalInput,\n    disabled: disabled,\n    readOnly: readOnly\n  }))));\n});\nvar InputNumber = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var disabled = props.disabled,\n    style = props.style,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-input-number' : _props$prefixCls,\n    value = props.value,\n    prefix = props.prefix,\n    suffix = props.suffix,\n    addonBefore = props.addonBefore,\n    addonAfter = props.addonAfter,\n    className = props.className,\n    classNames = props.classNames,\n    rest = _objectWithoutProperties(props, _excluded2);\n  var holderRef = React.useRef(null);\n  var inputNumberDomRef = React.useRef(null);\n  var inputFocusRef = React.useRef(null);\n  var focus = function focus(option) {\n    if (inputFocusRef.current) {\n      triggerFocus(inputFocusRef.current, option);\n    }\n  };\n  React.useImperativeHandle(ref, function () {\n    return proxyObject(inputFocusRef.current, {\n      focus: focus,\n      nativeElement: holderRef.current.nativeElement || inputNumberDomRef.current\n    });\n  });\n  return /*#__PURE__*/React.createElement(BaseInput, {\n    className: className,\n    triggerFocus: focus,\n    prefixCls: prefixCls,\n    value: value,\n    disabled: disabled,\n    style: style,\n    prefix: prefix,\n    suffix: suffix,\n    addonAfter: addonAfter,\n    addonBefore: addonBefore,\n    classNames: classNames,\n    components: {\n      affixWrapper: 'div',\n      groupWrapper: 'div',\n      wrapper: 'div',\n      groupAddon: 'div'\n    },\n    ref: holderRef\n  }, /*#__PURE__*/React.createElement(InternalInputNumber, _extends({\n    prefixCls: prefixCls,\n    disabled: disabled,\n    ref: inputFocusRef,\n    domRef: inputNumberDomRef,\n    className: classNames === null || classNames === void 0 ? void 0 : classNames.input\n  }, rest)));\n});\nif (process.env.NODE_ENV !== 'production') {\n  InputNumber.displayName = 'InputNumber';\n}\nexport default InputNumber;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_typeof", "_slicedToArray", "_objectWithoutProperties", "_excluded", "_excluded2", "getMiniDecimal", "getNumberPrecision", "num2str", "toFixed", "validateNumber", "clsx", "BaseInput", "useLayoutUpdateEffect", "proxyObject", "composeRef", "React", "useCursor", "<PERSON><PERSON><PERSON><PERSON>", "getDecupleSteps", "triggerFocus", "useFrame", "getDecimalValue", "stringMode", "decimalValue", "isEmpty", "toString", "toNumber", "getDecimalIfValidate", "value", "decimal", "isInvalidate", "InternalInputNumber", "forwardRef", "props", "ref", "prefixCls", "className", "style", "min", "max", "_props$step", "step", "defaultValue", "disabled", "readOnly", "up<PERSON><PERSON><PERSON>", "downHandler", "keyboard", "_props$changeOnWheel", "changeOnWheel", "_props$controls", "controls", "classNames", "parser", "formatter", "precision", "decimalSeparator", "onChange", "onInput", "onPressEnter", "onStep", "_props$changeOnBlur", "changeOnBlur", "domRef", "inputProps", "inputClassName", "concat", "inputRef", "useRef", "_React$useState", "useState", "_React$useState2", "focus", "setFocus", "userTypingRef", "compositionRef", "shiftKeyRef", "_React$useState3", "_React$useState4", "setDecimalValue", "setUncontrolledDecimalValue", "newDecimal", "undefined", "getPrecision", "useCallback", "numStr", "userTyping", "Math", "mergedParser", "num", "String", "parsedStr", "replace", "inputValueRef", "mergedFormatter", "number", "input", "current", "str", "mergedPrecision", "separatorStr", "_React$useState5", "initValue", "includes", "Number", "isNaN", "_React$useState6", "inputValue", "setInternalInputValue", "setInputValue", "newValue", "maxDecimal", "useMemo", "minDecimal", "upDisabled", "lessEquals", "downDisabled", "_useCursor", "_useCursor2", "recordCursor", "restoreCursor", "getRangeValue", "target", "isInRange", "triggerValueUpdate", "updateValue", "isRangeValidate", "equals", "onNextPromise", "collectInputValue", "inputStr", "finalValue", "finalDecimal", "nextInputStr", "onCompositionStart", "onCompositionEnd", "onInternalInput", "e", "onInternalStep", "up", "_inputRef$current", "stepDecimal", "negate", "add", "updatedValue", "offset", "type", "flushInputValue", "parsedValue", "formatValue", "onBeforeInput", "onKeyDown", "event", "key", "shift<PERSON>ey", "preventDefault", "onKeyUp", "useEffect", "onWheel", "deltaY", "addEventListener", "passive", "removeEventListener", "onBlur", "currentParsedValue", "createElement", "onFocus", "upNode", "downNode", "autoComplete", "role", "InputNumber", "_props$prefixCls", "prefix", "suffix", "addonBefore", "addonAfter", "rest", "holder<PERSON><PERSON>", "inputNumberDomRef", "inputFocusRef", "option", "useImperativeHandle", "nativeElement", "components", "affixWrapper", "groupWrapper", "wrapper", "groupAddon", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Desktop/code/system/frontend/node_modules/rc-input-number/es/InputNumber.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"className\", \"style\", \"min\", \"max\", \"step\", \"defaultValue\", \"value\", \"disabled\", \"readOnly\", \"upHandler\", \"downHandler\", \"keyboard\", \"changeOnWheel\", \"controls\", \"classNames\", \"stringMode\", \"parser\", \"formatter\", \"precision\", \"decimalSeparator\", \"onChange\", \"onInput\", \"onPressEnter\", \"onStep\", \"changeOnBlur\", \"domRef\"],\n  _excluded2 = [\"disabled\", \"style\", \"prefixCls\", \"value\", \"prefix\", \"suffix\", \"addonBefore\", \"addonAfter\", \"className\", \"classNames\"];\nimport getMiniDecimal, { getNumberPrecision, num2str, toFixed, validateNumber } from '@rc-component/mini-decimal';\nimport clsx from 'classnames';\nimport { BaseInput } from 'rc-input';\nimport { useLayoutUpdateEffect } from \"rc-util/es/hooks/useLayoutEffect\";\nimport proxyObject from \"rc-util/es/proxyObject\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport useCursor from \"./hooks/useCursor\";\nimport StepHandler from \"./StepHandler\";\nimport { getDecupleSteps } from \"./utils/numberUtil\";\nimport { triggerFocus } from \"rc-input/es/utils/commonUtils\";\nimport useFrame from \"./hooks/useFrame\";\n/**\n * We support `stringMode` which need handle correct type when user call in onChange\n * format max or min value\n * 1. if isInvalid return null\n * 2. if precision is undefined, return decimal\n * 3. format with precision\n *    I. if max > 0, round down with precision. Example: max= 3.5, precision=0  afterFormat: 3\n *    II. if max < 0, round up with precision. Example: max= -3.5, precision=0  afterFormat: -4\n *    III. if min > 0, round up with precision. Example: min= 3.5, precision=0  afterFormat: 4\n *    IV. if min < 0, round down with precision. Example: max= -3.5, precision=0  afterFormat: -3\n */\nvar getDecimalValue = function getDecimalValue(stringMode, decimalValue) {\n  if (stringMode || decimalValue.isEmpty()) {\n    return decimalValue.toString();\n  }\n  return decimalValue.toNumber();\n};\nvar getDecimalIfValidate = function getDecimalIfValidate(value) {\n  var decimal = getMiniDecimal(value);\n  return decimal.isInvalidate() ? null : decimal;\n};\nvar InternalInputNumber = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    min = props.min,\n    max = props.max,\n    _props$step = props.step,\n    step = _props$step === void 0 ? 1 : _props$step,\n    defaultValue = props.defaultValue,\n    value = props.value,\n    disabled = props.disabled,\n    readOnly = props.readOnly,\n    upHandler = props.upHandler,\n    downHandler = props.downHandler,\n    keyboard = props.keyboard,\n    _props$changeOnWheel = props.changeOnWheel,\n    changeOnWheel = _props$changeOnWheel === void 0 ? false : _props$changeOnWheel,\n    _props$controls = props.controls,\n    controls = _props$controls === void 0 ? true : _props$controls,\n    classNames = props.classNames,\n    stringMode = props.stringMode,\n    parser = props.parser,\n    formatter = props.formatter,\n    precision = props.precision,\n    decimalSeparator = props.decimalSeparator,\n    onChange = props.onChange,\n    onInput = props.onInput,\n    onPressEnter = props.onPressEnter,\n    onStep = props.onStep,\n    _props$changeOnBlur = props.changeOnBlur,\n    changeOnBlur = _props$changeOnBlur === void 0 ? true : _props$changeOnBlur,\n    domRef = props.domRef,\n    inputProps = _objectWithoutProperties(props, _excluded);\n  var inputClassName = \"\".concat(prefixCls, \"-input\");\n  var inputRef = React.useRef(null);\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    focus = _React$useState2[0],\n    setFocus = _React$useState2[1];\n  var userTypingRef = React.useRef(false);\n  var compositionRef = React.useRef(false);\n  var shiftKeyRef = React.useRef(false);\n\n  // ============================ Value =============================\n  // Real value control\n  var _React$useState3 = React.useState(function () {\n      return getMiniDecimal(value !== null && value !== void 0 ? value : defaultValue);\n    }),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    decimalValue = _React$useState4[0],\n    setDecimalValue = _React$useState4[1];\n  function setUncontrolledDecimalValue(newDecimal) {\n    if (value === undefined) {\n      setDecimalValue(newDecimal);\n    }\n  }\n\n  // ====================== Parser & Formatter ======================\n  /**\n   * `precision` is used for formatter & onChange.\n   * It will auto generate by `value` & `step`.\n   * But it will not block user typing.\n   *\n   * Note: Auto generate `precision` is used for legacy logic.\n   * We should remove this since we already support high precision with BigInt.\n   *\n   * @param number  Provide which number should calculate precision\n   * @param userTyping  Change by user typing\n   */\n  var getPrecision = React.useCallback(function (numStr, userTyping) {\n    if (userTyping) {\n      return undefined;\n    }\n    if (precision >= 0) {\n      return precision;\n    }\n    return Math.max(getNumberPrecision(numStr), getNumberPrecision(step));\n  }, [precision, step]);\n\n  // >>> Parser\n  var mergedParser = React.useCallback(function (num) {\n    var numStr = String(num);\n    if (parser) {\n      return parser(numStr);\n    }\n    var parsedStr = numStr;\n    if (decimalSeparator) {\n      parsedStr = parsedStr.replace(decimalSeparator, '.');\n    }\n\n    // [Legacy] We still support auto convert `$ 123,456` to `123456`\n    return parsedStr.replace(/[^\\w.-]+/g, '');\n  }, [parser, decimalSeparator]);\n\n  // >>> Formatter\n  var inputValueRef = React.useRef('');\n  var mergedFormatter = React.useCallback(function (number, userTyping) {\n    if (formatter) {\n      return formatter(number, {\n        userTyping: userTyping,\n        input: String(inputValueRef.current)\n      });\n    }\n    var str = typeof number === 'number' ? num2str(number) : number;\n\n    // User typing will not auto format with precision directly\n    if (!userTyping) {\n      var mergedPrecision = getPrecision(str, userTyping);\n      if (validateNumber(str) && (decimalSeparator || mergedPrecision >= 0)) {\n        // Separator\n        var separatorStr = decimalSeparator || '.';\n        str = toFixed(str, separatorStr, mergedPrecision);\n      }\n    }\n    return str;\n  }, [formatter, getPrecision, decimalSeparator]);\n\n  // ========================== InputValue ==========================\n  /**\n   * Input text value control\n   *\n   * User can not update input content directly. It updates with follow rules by priority:\n   *  1. controlled `value` changed\n   *    * [SPECIAL] Typing like `1.` should not immediately convert to `1`\n   *  2. User typing with format (not precision)\n   *  3. Blur or Enter trigger revalidate\n   */\n  var _React$useState5 = React.useState(function () {\n      var initValue = defaultValue !== null && defaultValue !== void 0 ? defaultValue : value;\n      if (decimalValue.isInvalidate() && ['string', 'number'].includes(_typeof(initValue))) {\n        return Number.isNaN(initValue) ? '' : initValue;\n      }\n      return mergedFormatter(decimalValue.toString(), false);\n    }),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    inputValue = _React$useState6[0],\n    setInternalInputValue = _React$useState6[1];\n  inputValueRef.current = inputValue;\n\n  // Should always be string\n  function setInputValue(newValue, userTyping) {\n    setInternalInputValue(mergedFormatter(\n    // Invalidate number is sometime passed by external control, we should let it go\n    // Otherwise is controlled by internal interactive logic which check by userTyping\n    // You can ref 'show limited value when input is not focused' test for more info.\n    newValue.isInvalidate() ? newValue.toString(false) : newValue.toString(!userTyping), userTyping));\n  }\n\n  // >>> Max & Min limit\n  var maxDecimal = React.useMemo(function () {\n    return getDecimalIfValidate(max);\n  }, [max, precision]);\n  var minDecimal = React.useMemo(function () {\n    return getDecimalIfValidate(min);\n  }, [min, precision]);\n  var upDisabled = React.useMemo(function () {\n    if (!maxDecimal || !decimalValue || decimalValue.isInvalidate()) {\n      return false;\n    }\n    return maxDecimal.lessEquals(decimalValue);\n  }, [maxDecimal, decimalValue]);\n  var downDisabled = React.useMemo(function () {\n    if (!minDecimal || !decimalValue || decimalValue.isInvalidate()) {\n      return false;\n    }\n    return decimalValue.lessEquals(minDecimal);\n  }, [minDecimal, decimalValue]);\n\n  // Cursor controller\n  var _useCursor = useCursor(inputRef.current, focus),\n    _useCursor2 = _slicedToArray(_useCursor, 2),\n    recordCursor = _useCursor2[0],\n    restoreCursor = _useCursor2[1];\n\n  // ============================= Data =============================\n  /**\n   * Find target value closet within range.\n   * e.g. [11, 28]:\n   *    3  => 11\n   *    23 => 23\n   *    99 => 28\n   */\n  var getRangeValue = function getRangeValue(target) {\n    // target > max\n    if (maxDecimal && !target.lessEquals(maxDecimal)) {\n      return maxDecimal;\n    }\n\n    // target < min\n    if (minDecimal && !minDecimal.lessEquals(target)) {\n      return minDecimal;\n    }\n    return null;\n  };\n\n  /**\n   * Check value is in [min, max] range\n   */\n  var isInRange = function isInRange(target) {\n    return !getRangeValue(target);\n  };\n\n  /**\n   * Trigger `onChange` if value validated and not equals of origin.\n   * Return the value that re-align in range.\n   */\n  var triggerValueUpdate = function triggerValueUpdate(newValue, userTyping) {\n    var updateValue = newValue;\n    var isRangeValidate = isInRange(updateValue) || updateValue.isEmpty();\n\n    // Skip align value when trigger value is empty.\n    // We just trigger onChange(null)\n    // This should not block user typing\n    if (!updateValue.isEmpty() && !userTyping) {\n      // Revert value in range if needed\n      updateValue = getRangeValue(updateValue) || updateValue;\n      isRangeValidate = true;\n    }\n    if (!readOnly && !disabled && isRangeValidate) {\n      var numStr = updateValue.toString();\n      var mergedPrecision = getPrecision(numStr, userTyping);\n      if (mergedPrecision >= 0) {\n        updateValue = getMiniDecimal(toFixed(numStr, '.', mergedPrecision));\n\n        // When to fixed. The value may out of min & max range.\n        // 4 in [0, 3.8] => 3.8 => 4 (toFixed)\n        if (!isInRange(updateValue)) {\n          updateValue = getMiniDecimal(toFixed(numStr, '.', mergedPrecision, true));\n        }\n      }\n\n      // Trigger event\n      if (!updateValue.equals(decimalValue)) {\n        setUncontrolledDecimalValue(updateValue);\n        onChange === null || onChange === void 0 || onChange(updateValue.isEmpty() ? null : getDecimalValue(stringMode, updateValue));\n\n        // Reformat input if value is not controlled\n        if (value === undefined) {\n          setInputValue(updateValue, userTyping);\n        }\n      }\n      return updateValue;\n    }\n    return decimalValue;\n  };\n\n  // ========================== User Input ==========================\n  var onNextPromise = useFrame();\n\n  // >>> Collect input value\n  var collectInputValue = function collectInputValue(inputStr) {\n    recordCursor();\n\n    // Update inputValue in case input can not parse as number\n    // Refresh ref value immediately since it may used by formatter\n    inputValueRef.current = inputStr;\n    setInternalInputValue(inputStr);\n\n    // Parse number\n    if (!compositionRef.current) {\n      var finalValue = mergedParser(inputStr);\n      var finalDecimal = getMiniDecimal(finalValue);\n      if (!finalDecimal.isNaN()) {\n        triggerValueUpdate(finalDecimal, true);\n      }\n    }\n\n    // Trigger onInput later to let user customize value if they want to handle something after onChange\n    onInput === null || onInput === void 0 || onInput(inputStr);\n\n    // optimize for chinese input experience\n    // https://github.com/ant-design/ant-design/issues/8196\n    onNextPromise(function () {\n      var nextInputStr = inputStr;\n      if (!parser) {\n        nextInputStr = inputStr.replace(/。/g, '.');\n      }\n      if (nextInputStr !== inputStr) {\n        collectInputValue(nextInputStr);\n      }\n    });\n  };\n\n  // >>> Composition\n  var onCompositionStart = function onCompositionStart() {\n    compositionRef.current = true;\n  };\n  var onCompositionEnd = function onCompositionEnd() {\n    compositionRef.current = false;\n    collectInputValue(inputRef.current.value);\n  };\n\n  // >>> Input\n  var onInternalInput = function onInternalInput(e) {\n    collectInputValue(e.target.value);\n  };\n\n  // ============================= Step =============================\n  var onInternalStep = function onInternalStep(up) {\n    var _inputRef$current;\n    // Ignore step since out of range\n    if (up && upDisabled || !up && downDisabled) {\n      return;\n    }\n\n    // Clear typing status since it may be caused by up & down key.\n    // We should sync with input value.\n    userTypingRef.current = false;\n    var stepDecimal = getMiniDecimal(shiftKeyRef.current ? getDecupleSteps(step) : step);\n    if (!up) {\n      stepDecimal = stepDecimal.negate();\n    }\n    var target = (decimalValue || getMiniDecimal(0)).add(stepDecimal.toString());\n    var updatedValue = triggerValueUpdate(target, false);\n    onStep === null || onStep === void 0 || onStep(getDecimalValue(stringMode, updatedValue), {\n      offset: shiftKeyRef.current ? getDecupleSteps(step) : step,\n      type: up ? 'up' : 'down'\n    });\n    (_inputRef$current = inputRef.current) === null || _inputRef$current === void 0 || _inputRef$current.focus();\n  };\n\n  // ============================ Flush =============================\n  /**\n   * Flush current input content to trigger value change & re-formatter input if needed.\n   * This will always flush input value for update.\n   * If it's invalidate, will fallback to last validate value.\n   */\n  var flushInputValue = function flushInputValue(userTyping) {\n    var parsedValue = getMiniDecimal(mergedParser(inputValue));\n    var formatValue;\n    if (!parsedValue.isNaN()) {\n      // Only validate value or empty value can be re-fill to inputValue\n      // Reassign the formatValue within ranged of trigger control\n      formatValue = triggerValueUpdate(parsedValue, userTyping);\n    } else {\n      formatValue = triggerValueUpdate(decimalValue, userTyping);\n    }\n    if (value !== undefined) {\n      // Reset back with controlled value first\n      setInputValue(decimalValue, false);\n    } else if (!formatValue.isNaN()) {\n      // Reset input back since no validate value\n      setInputValue(formatValue, false);\n    }\n  };\n\n  // Solve the issue of the event triggering sequence when entering numbers in chinese input (Safari)\n  var onBeforeInput = function onBeforeInput() {\n    userTypingRef.current = true;\n  };\n  var onKeyDown = function onKeyDown(event) {\n    var key = event.key,\n      shiftKey = event.shiftKey;\n    userTypingRef.current = true;\n    shiftKeyRef.current = shiftKey;\n    if (key === 'Enter') {\n      if (!compositionRef.current) {\n        userTypingRef.current = false;\n      }\n      flushInputValue(false);\n      onPressEnter === null || onPressEnter === void 0 || onPressEnter(event);\n    }\n    if (keyboard === false) {\n      return;\n    }\n\n    // Do step\n    if (!compositionRef.current && ['Up', 'ArrowUp', 'Down', 'ArrowDown'].includes(key)) {\n      onInternalStep(key === 'Up' || key === 'ArrowUp');\n      event.preventDefault();\n    }\n  };\n  var onKeyUp = function onKeyUp() {\n    userTypingRef.current = false;\n    shiftKeyRef.current = false;\n  };\n  React.useEffect(function () {\n    if (changeOnWheel && focus) {\n      var onWheel = function onWheel(event) {\n        // moving mouse wheel rises wheel event with deltaY < 0\n        // scroll value grows from top to bottom, as screen Y coordinate\n        onInternalStep(event.deltaY < 0);\n        event.preventDefault();\n      };\n      var input = inputRef.current;\n      if (input) {\n        // React onWheel is passive and we can't preventDefault() in it.\n        // That's why we should subscribe with DOM listener\n        // https://stackoverflow.com/questions/63663025/react-onwheel-handler-cant-preventdefault-because-its-a-passive-event-listenev\n        input.addEventListener('wheel', onWheel, {\n          passive: false\n        });\n        return function () {\n          return input.removeEventListener('wheel', onWheel);\n        };\n      }\n    }\n  });\n\n  // >>> Focus & Blur\n  var onBlur = function onBlur() {\n    if (changeOnBlur) {\n      flushInputValue(false);\n    }\n    setFocus(false);\n    userTypingRef.current = false;\n  };\n\n  // ========================== Controlled ==========================\n  // Input by precision & formatter\n  useLayoutUpdateEffect(function () {\n    if (!decimalValue.isInvalidate()) {\n      setInputValue(decimalValue, false);\n    }\n  }, [precision, formatter]);\n\n  // Input by value\n  useLayoutUpdateEffect(function () {\n    var newValue = getMiniDecimal(value);\n    setDecimalValue(newValue);\n    var currentParsedValue = getMiniDecimal(mergedParser(inputValue));\n\n    // When user typing from `1.2` to `1.`, we should not convert to `1` immediately.\n    // But let it go if user set `formatter`\n    if (!newValue.equals(currentParsedValue) || !userTypingRef.current || formatter) {\n      // Update value as effect\n      setInputValue(newValue, userTypingRef.current);\n    }\n  }, [value]);\n\n  // ============================ Cursor ============================\n  useLayoutUpdateEffect(function () {\n    if (formatter) {\n      restoreCursor();\n    }\n  }, [inputValue]);\n\n  // ============================ Render ============================\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: domRef,\n    className: clsx(prefixCls, className, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-focused\"), focus), \"\".concat(prefixCls, \"-disabled\"), disabled), \"\".concat(prefixCls, \"-readonly\"), readOnly), \"\".concat(prefixCls, \"-not-a-number\"), decimalValue.isNaN()), \"\".concat(prefixCls, \"-out-of-range\"), !decimalValue.isInvalidate() && !isInRange(decimalValue))),\n    style: style,\n    onFocus: function onFocus() {\n      setFocus(true);\n    },\n    onBlur: onBlur,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp,\n    onCompositionStart: onCompositionStart,\n    onCompositionEnd: onCompositionEnd,\n    onBeforeInput: onBeforeInput\n  }, controls && /*#__PURE__*/React.createElement(StepHandler, {\n    prefixCls: prefixCls,\n    upNode: upHandler,\n    downNode: downHandler,\n    upDisabled: upDisabled,\n    downDisabled: downDisabled,\n    onStep: onInternalStep\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(inputClassName, \"-wrap\")\n  }, /*#__PURE__*/React.createElement(\"input\", _extends({\n    autoComplete: \"off\",\n    role: \"spinbutton\",\n    \"aria-valuemin\": min,\n    \"aria-valuemax\": max,\n    \"aria-valuenow\": decimalValue.isInvalidate() ? null : decimalValue.toString(),\n    step: step\n  }, inputProps, {\n    ref: composeRef(inputRef, ref),\n    className: inputClassName,\n    value: inputValue,\n    onChange: onInternalInput,\n    disabled: disabled,\n    readOnly: readOnly\n  }))));\n});\nvar InputNumber = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var disabled = props.disabled,\n    style = props.style,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-input-number' : _props$prefixCls,\n    value = props.value,\n    prefix = props.prefix,\n    suffix = props.suffix,\n    addonBefore = props.addonBefore,\n    addonAfter = props.addonAfter,\n    className = props.className,\n    classNames = props.classNames,\n    rest = _objectWithoutProperties(props, _excluded2);\n  var holderRef = React.useRef(null);\n  var inputNumberDomRef = React.useRef(null);\n  var inputFocusRef = React.useRef(null);\n  var focus = function focus(option) {\n    if (inputFocusRef.current) {\n      triggerFocus(inputFocusRef.current, option);\n    }\n  };\n  React.useImperativeHandle(ref, function () {\n    return proxyObject(inputFocusRef.current, {\n      focus: focus,\n      nativeElement: holderRef.current.nativeElement || inputNumberDomRef.current\n    });\n  });\n  return /*#__PURE__*/React.createElement(BaseInput, {\n    className: className,\n    triggerFocus: focus,\n    prefixCls: prefixCls,\n    value: value,\n    disabled: disabled,\n    style: style,\n    prefix: prefix,\n    suffix: suffix,\n    addonAfter: addonAfter,\n    addonBefore: addonBefore,\n    classNames: classNames,\n    components: {\n      affixWrapper: 'div',\n      groupWrapper: 'div',\n      wrapper: 'div',\n      groupAddon: 'div'\n    },\n    ref: holderRef\n  }, /*#__PURE__*/React.createElement(InternalInputNumber, _extends({\n    prefixCls: prefixCls,\n    disabled: disabled,\n    ref: inputFocusRef,\n    domRef: inputNumberDomRef,\n    className: classNames === null || classNames === void 0 ? void 0 : classNames.input\n  }, rest)));\n});\nif (process.env.NODE_ENV !== 'production') {\n  InputNumber.displayName = 'InputNumber';\n}\nexport default InputNumber;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,aAAa,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,kBAAkB,EAAE,UAAU,EAAE,SAAS,EAAE,cAAc,EAAE,QAAQ,EAAE,cAAc,EAAE,QAAQ,CAAC;EAC5VC,UAAU,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC;AACtI,OAAOC,cAAc,IAAIC,kBAAkB,EAAEC,OAAO,EAAEC,OAAO,EAAEC,cAAc,QAAQ,4BAA4B;AACjH,OAAOC,IAAI,MAAM,YAAY;AAC7B,SAASC,SAAS,QAAQ,UAAU;AACpC,SAASC,qBAAqB,QAAQ,kCAAkC;AACxE,OAAOC,WAAW,MAAM,wBAAwB;AAChD,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,YAAY,QAAQ,+BAA+B;AAC5D,OAAOC,QAAQ,MAAM,kBAAkB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,UAAU,EAAEC,YAAY,EAAE;EACvE,IAAID,UAAU,IAAIC,YAAY,CAACC,OAAO,CAAC,CAAC,EAAE;IACxC,OAAOD,YAAY,CAACE,QAAQ,CAAC,CAAC;EAChC;EACA,OAAOF,YAAY,CAACG,QAAQ,CAAC,CAAC;AAChC,CAAC;AACD,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,KAAK,EAAE;EAC9D,IAAIC,OAAO,GAAGxB,cAAc,CAACuB,KAAK,CAAC;EACnC,OAAOC,OAAO,CAACC,YAAY,CAAC,CAAC,GAAG,IAAI,GAAGD,OAAO;AAChD,CAAC;AACD,IAAIE,mBAAmB,GAAG,aAAahB,KAAK,CAACiB,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC5E,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,GAAG,GAAGL,KAAK,CAACK,GAAG;IACfC,GAAG,GAAGN,KAAK,CAACM,GAAG;IACfC,WAAW,GAAGP,KAAK,CAACQ,IAAI;IACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,WAAW;IAC/CE,YAAY,GAAGT,KAAK,CAACS,YAAY;IACjCd,KAAK,GAAGK,KAAK,CAACL,KAAK;IACnBe,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBC,QAAQ,GAAGX,KAAK,CAACW,QAAQ;IACzBC,SAAS,GAAGZ,KAAK,CAACY,SAAS;IAC3BC,WAAW,GAAGb,KAAK,CAACa,WAAW;IAC/BC,QAAQ,GAAGd,KAAK,CAACc,QAAQ;IACzBC,oBAAoB,GAAGf,KAAK,CAACgB,aAAa;IAC1CA,aAAa,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,oBAAoB;IAC9EE,eAAe,GAAGjB,KAAK,CAACkB,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,eAAe;IAC9DE,UAAU,GAAGnB,KAAK,CAACmB,UAAU;IAC7B9B,UAAU,GAAGW,KAAK,CAACX,UAAU;IAC7B+B,MAAM,GAAGpB,KAAK,CAACoB,MAAM;IACrBC,SAAS,GAAGrB,KAAK,CAACqB,SAAS;IAC3BC,SAAS,GAAGtB,KAAK,CAACsB,SAAS;IAC3BC,gBAAgB,GAAGvB,KAAK,CAACuB,gBAAgB;IACzCC,QAAQ,GAAGxB,KAAK,CAACwB,QAAQ;IACzBC,OAAO,GAAGzB,KAAK,CAACyB,OAAO;IACvBC,YAAY,GAAG1B,KAAK,CAAC0B,YAAY;IACjCC,MAAM,GAAG3B,KAAK,CAAC2B,MAAM;IACrBC,mBAAmB,GAAG5B,KAAK,CAAC6B,YAAY;IACxCA,YAAY,GAAGD,mBAAmB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,mBAAmB;IAC1EE,MAAM,GAAG9B,KAAK,CAAC8B,MAAM;IACrBC,UAAU,GAAG9D,wBAAwB,CAAC+B,KAAK,EAAE9B,SAAS,CAAC;EACzD,IAAI8D,cAAc,GAAG,EAAE,CAACC,MAAM,CAAC/B,SAAS,EAAE,QAAQ,CAAC;EACnD,IAAIgC,QAAQ,GAAGpD,KAAK,CAACqD,MAAM,CAAC,IAAI,CAAC;EACjC,IAAIC,eAAe,GAAGtD,KAAK,CAACuD,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGtE,cAAc,CAACoE,eAAe,EAAE,CAAC,CAAC;IACrDG,KAAK,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC3BE,QAAQ,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAChC,IAAIG,aAAa,GAAG3D,KAAK,CAACqD,MAAM,CAAC,KAAK,CAAC;EACvC,IAAIO,cAAc,GAAG5D,KAAK,CAACqD,MAAM,CAAC,KAAK,CAAC;EACxC,IAAIQ,WAAW,GAAG7D,KAAK,CAACqD,MAAM,CAAC,KAAK,CAAC;;EAErC;EACA;EACA,IAAIS,gBAAgB,GAAG9D,KAAK,CAACuD,QAAQ,CAAC,YAAY;MAC9C,OAAOjE,cAAc,CAACuB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGc,YAAY,CAAC;IAClF,CAAC,CAAC;IACFoC,gBAAgB,GAAG7E,cAAc,CAAC4E,gBAAgB,EAAE,CAAC,CAAC;IACtDtD,YAAY,GAAGuD,gBAAgB,CAAC,CAAC,CAAC;IAClCC,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EACvC,SAASE,2BAA2BA,CAACC,UAAU,EAAE;IAC/C,IAAIrD,KAAK,KAAKsD,SAAS,EAAE;MACvBH,eAAe,CAACE,UAAU,CAAC;IAC7B;EACF;;EAEA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIE,YAAY,GAAGpE,KAAK,CAACqE,WAAW,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IACjE,IAAIA,UAAU,EAAE;MACd,OAAOJ,SAAS;IAClB;IACA,IAAI3B,SAAS,IAAI,CAAC,EAAE;MAClB,OAAOA,SAAS;IAClB;IACA,OAAOgC,IAAI,CAAChD,GAAG,CAACjC,kBAAkB,CAAC+E,MAAM,CAAC,EAAE/E,kBAAkB,CAACmC,IAAI,CAAC,CAAC;EACvE,CAAC,EAAE,CAACc,SAAS,EAAEd,IAAI,CAAC,CAAC;;EAErB;EACA,IAAI+C,YAAY,GAAGzE,KAAK,CAACqE,WAAW,CAAC,UAAUK,GAAG,EAAE;IAClD,IAAIJ,MAAM,GAAGK,MAAM,CAACD,GAAG,CAAC;IACxB,IAAIpC,MAAM,EAAE;MACV,OAAOA,MAAM,CAACgC,MAAM,CAAC;IACvB;IACA,IAAIM,SAAS,GAAGN,MAAM;IACtB,IAAI7B,gBAAgB,EAAE;MACpBmC,SAAS,GAAGA,SAAS,CAACC,OAAO,CAACpC,gBAAgB,EAAE,GAAG,CAAC;IACtD;;IAEA;IACA,OAAOmC,SAAS,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;EAC3C,CAAC,EAAE,CAACvC,MAAM,EAAEG,gBAAgB,CAAC,CAAC;;EAE9B;EACA,IAAIqC,aAAa,GAAG9E,KAAK,CAACqD,MAAM,CAAC,EAAE,CAAC;EACpC,IAAI0B,eAAe,GAAG/E,KAAK,CAACqE,WAAW,CAAC,UAAUW,MAAM,EAAET,UAAU,EAAE;IACpE,IAAIhC,SAAS,EAAE;MACb,OAAOA,SAAS,CAACyC,MAAM,EAAE;QACvBT,UAAU,EAAEA,UAAU;QACtBU,KAAK,EAAEN,MAAM,CAACG,aAAa,CAACI,OAAO;MACrC,CAAC,CAAC;IACJ;IACA,IAAIC,GAAG,GAAG,OAAOH,MAAM,KAAK,QAAQ,GAAGxF,OAAO,CAACwF,MAAM,CAAC,GAAGA,MAAM;;IAE/D;IACA,IAAI,CAACT,UAAU,EAAE;MACf,IAAIa,eAAe,GAAGhB,YAAY,CAACe,GAAG,EAAEZ,UAAU,CAAC;MACnD,IAAI7E,cAAc,CAACyF,GAAG,CAAC,KAAK1C,gBAAgB,IAAI2C,eAAe,IAAI,CAAC,CAAC,EAAE;QACrE;QACA,IAAIC,YAAY,GAAG5C,gBAAgB,IAAI,GAAG;QAC1C0C,GAAG,GAAG1F,OAAO,CAAC0F,GAAG,EAAEE,YAAY,EAAED,eAAe,CAAC;MACnD;IACF;IACA,OAAOD,GAAG;EACZ,CAAC,EAAE,CAAC5C,SAAS,EAAE6B,YAAY,EAAE3B,gBAAgB,CAAC,CAAC;;EAE/C;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAI6C,gBAAgB,GAAGtF,KAAK,CAACuD,QAAQ,CAAC,YAAY;MAC9C,IAAIgC,SAAS,GAAG5D,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAGd,KAAK;MACvF,IAAIL,YAAY,CAACO,YAAY,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAACyE,QAAQ,CAACvG,OAAO,CAACsG,SAAS,CAAC,CAAC,EAAE;QACpF,OAAOE,MAAM,CAACC,KAAK,CAACH,SAAS,CAAC,GAAG,EAAE,GAAGA,SAAS;MACjD;MACA,OAAOR,eAAe,CAACvE,YAAY,CAACE,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC;IACxD,CAAC,CAAC;IACFiF,gBAAgB,GAAGzG,cAAc,CAACoG,gBAAgB,EAAE,CAAC,CAAC;IACtDM,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,qBAAqB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC7Cb,aAAa,CAACI,OAAO,GAAGU,UAAU;;EAElC;EACA,SAASE,aAAaA,CAACC,QAAQ,EAAExB,UAAU,EAAE;IAC3CsB,qBAAqB,CAACd,eAAe;IACrC;IACA;IACA;IACAgB,QAAQ,CAAChF,YAAY,CAAC,CAAC,GAAGgF,QAAQ,CAACrF,QAAQ,CAAC,KAAK,CAAC,GAAGqF,QAAQ,CAACrF,QAAQ,CAAC,CAAC6D,UAAU,CAAC,EAAEA,UAAU,CAAC,CAAC;EACnG;;EAEA;EACA,IAAIyB,UAAU,GAAGhG,KAAK,CAACiG,OAAO,CAAC,YAAY;IACzC,OAAOrF,oBAAoB,CAACY,GAAG,CAAC;EAClC,CAAC,EAAE,CAACA,GAAG,EAAEgB,SAAS,CAAC,CAAC;EACpB,IAAI0D,UAAU,GAAGlG,KAAK,CAACiG,OAAO,CAAC,YAAY;IACzC,OAAOrF,oBAAoB,CAACW,GAAG,CAAC;EAClC,CAAC,EAAE,CAACA,GAAG,EAAEiB,SAAS,CAAC,CAAC;EACpB,IAAI2D,UAAU,GAAGnG,KAAK,CAACiG,OAAO,CAAC,YAAY;IACzC,IAAI,CAACD,UAAU,IAAI,CAACxF,YAAY,IAAIA,YAAY,CAACO,YAAY,CAAC,CAAC,EAAE;MAC/D,OAAO,KAAK;IACd;IACA,OAAOiF,UAAU,CAACI,UAAU,CAAC5F,YAAY,CAAC;EAC5C,CAAC,EAAE,CAACwF,UAAU,EAAExF,YAAY,CAAC,CAAC;EAC9B,IAAI6F,YAAY,GAAGrG,KAAK,CAACiG,OAAO,CAAC,YAAY;IAC3C,IAAI,CAACC,UAAU,IAAI,CAAC1F,YAAY,IAAIA,YAAY,CAACO,YAAY,CAAC,CAAC,EAAE;MAC/D,OAAO,KAAK;IACd;IACA,OAAOP,YAAY,CAAC4F,UAAU,CAACF,UAAU,CAAC;EAC5C,CAAC,EAAE,CAACA,UAAU,EAAE1F,YAAY,CAAC,CAAC;;EAE9B;EACA,IAAI8F,UAAU,GAAGrG,SAAS,CAACmD,QAAQ,CAAC8B,OAAO,EAAEzB,KAAK,CAAC;IACjD8C,WAAW,GAAGrH,cAAc,CAACoH,UAAU,EAAE,CAAC,CAAC;IAC3CE,YAAY,GAAGD,WAAW,CAAC,CAAC,CAAC;IAC7BE,aAAa,GAAGF,WAAW,CAAC,CAAC,CAAC;;EAEhC;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAIG,aAAa,GAAG,SAASA,aAAaA,CAACC,MAAM,EAAE;IACjD;IACA,IAAIX,UAAU,IAAI,CAACW,MAAM,CAACP,UAAU,CAACJ,UAAU,CAAC,EAAE;MAChD,OAAOA,UAAU;IACnB;;IAEA;IACA,IAAIE,UAAU,IAAI,CAACA,UAAU,CAACE,UAAU,CAACO,MAAM,CAAC,EAAE;MAChD,OAAOT,UAAU;IACnB;IACA,OAAO,IAAI;EACb,CAAC;;EAED;AACF;AACA;EACE,IAAIU,SAAS,GAAG,SAASA,SAASA,CAACD,MAAM,EAAE;IACzC,OAAO,CAACD,aAAa,CAACC,MAAM,CAAC;EAC/B,CAAC;;EAED;AACF;AACA;AACA;EACE,IAAIE,kBAAkB,GAAG,SAASA,kBAAkBA,CAACd,QAAQ,EAAExB,UAAU,EAAE;IACzE,IAAIuC,WAAW,GAAGf,QAAQ;IAC1B,IAAIgB,eAAe,GAAGH,SAAS,CAACE,WAAW,CAAC,IAAIA,WAAW,CAACrG,OAAO,CAAC,CAAC;;IAErE;IACA;IACA;IACA,IAAI,CAACqG,WAAW,CAACrG,OAAO,CAAC,CAAC,IAAI,CAAC8D,UAAU,EAAE;MACzC;MACAuC,WAAW,GAAGJ,aAAa,CAACI,WAAW,CAAC,IAAIA,WAAW;MACvDC,eAAe,GAAG,IAAI;IACxB;IACA,IAAI,CAAClF,QAAQ,IAAI,CAACD,QAAQ,IAAImF,eAAe,EAAE;MAC7C,IAAIzC,MAAM,GAAGwC,WAAW,CAACpG,QAAQ,CAAC,CAAC;MACnC,IAAI0E,eAAe,GAAGhB,YAAY,CAACE,MAAM,EAAEC,UAAU,CAAC;MACtD,IAAIa,eAAe,IAAI,CAAC,EAAE;QACxB0B,WAAW,GAAGxH,cAAc,CAACG,OAAO,CAAC6E,MAAM,EAAE,GAAG,EAAEc,eAAe,CAAC,CAAC;;QAEnE;QACA;QACA,IAAI,CAACwB,SAAS,CAACE,WAAW,CAAC,EAAE;UAC3BA,WAAW,GAAGxH,cAAc,CAACG,OAAO,CAAC6E,MAAM,EAAE,GAAG,EAAEc,eAAe,EAAE,IAAI,CAAC,CAAC;QAC3E;MACF;;MAEA;MACA,IAAI,CAAC0B,WAAW,CAACE,MAAM,CAACxG,YAAY,CAAC,EAAE;QACrCyD,2BAA2B,CAAC6C,WAAW,CAAC;QACxCpE,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,IAAIA,QAAQ,CAACoE,WAAW,CAACrG,OAAO,CAAC,CAAC,GAAG,IAAI,GAAGH,eAAe,CAACC,UAAU,EAAEuG,WAAW,CAAC,CAAC;;QAE7H;QACA,IAAIjG,KAAK,KAAKsD,SAAS,EAAE;UACvB2B,aAAa,CAACgB,WAAW,EAAEvC,UAAU,CAAC;QACxC;MACF;MACA,OAAOuC,WAAW;IACpB;IACA,OAAOtG,YAAY;EACrB,CAAC;;EAED;EACA,IAAIyG,aAAa,GAAG5G,QAAQ,CAAC,CAAC;;EAE9B;EACA,IAAI6G,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,QAAQ,EAAE;IAC3DX,YAAY,CAAC,CAAC;;IAEd;IACA;IACA1B,aAAa,CAACI,OAAO,GAAGiC,QAAQ;IAChCtB,qBAAqB,CAACsB,QAAQ,CAAC;;IAE/B;IACA,IAAI,CAACvD,cAAc,CAACsB,OAAO,EAAE;MAC3B,IAAIkC,UAAU,GAAG3C,YAAY,CAAC0C,QAAQ,CAAC;MACvC,IAAIE,YAAY,GAAG/H,cAAc,CAAC8H,UAAU,CAAC;MAC7C,IAAI,CAACC,YAAY,CAAC3B,KAAK,CAAC,CAAC,EAAE;QACzBmB,kBAAkB,CAACQ,YAAY,EAAE,IAAI,CAAC;MACxC;IACF;;IAEA;IACA1E,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACwE,QAAQ,CAAC;;IAE3D;IACA;IACAF,aAAa,CAAC,YAAY;MACxB,IAAIK,YAAY,GAAGH,QAAQ;MAC3B,IAAI,CAAC7E,MAAM,EAAE;QACXgF,YAAY,GAAGH,QAAQ,CAACtC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;MAC5C;MACA,IAAIyC,YAAY,KAAKH,QAAQ,EAAE;QAC7BD,iBAAiB,CAACI,YAAY,CAAC;MACjC;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACrD3D,cAAc,CAACsB,OAAO,GAAG,IAAI;EAC/B,CAAC;EACD,IAAIsC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD5D,cAAc,CAACsB,OAAO,GAAG,KAAK;IAC9BgC,iBAAiB,CAAC9D,QAAQ,CAAC8B,OAAO,CAACrE,KAAK,CAAC;EAC3C,CAAC;;EAED;EACA,IAAI4G,eAAe,GAAG,SAASA,eAAeA,CAACC,CAAC,EAAE;IAChDR,iBAAiB,CAACQ,CAAC,CAACf,MAAM,CAAC9F,KAAK,CAAC;EACnC,CAAC;;EAED;EACA,IAAI8G,cAAc,GAAG,SAASA,cAAcA,CAACC,EAAE,EAAE;IAC/C,IAAIC,iBAAiB;IACrB;IACA,IAAID,EAAE,IAAIzB,UAAU,IAAI,CAACyB,EAAE,IAAIvB,YAAY,EAAE;MAC3C;IACF;;IAEA;IACA;IACA1C,aAAa,CAACuB,OAAO,GAAG,KAAK;IAC7B,IAAI4C,WAAW,GAAGxI,cAAc,CAACuE,WAAW,CAACqB,OAAO,GAAG/E,eAAe,CAACuB,IAAI,CAAC,GAAGA,IAAI,CAAC;IACpF,IAAI,CAACkG,EAAE,EAAE;MACPE,WAAW,GAAGA,WAAW,CAACC,MAAM,CAAC,CAAC;IACpC;IACA,IAAIpB,MAAM,GAAG,CAACnG,YAAY,IAAIlB,cAAc,CAAC,CAAC,CAAC,EAAE0I,GAAG,CAACF,WAAW,CAACpH,QAAQ,CAAC,CAAC,CAAC;IAC5E,IAAIuH,YAAY,GAAGpB,kBAAkB,CAACF,MAAM,EAAE,KAAK,CAAC;IACpD9D,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,IAAIA,MAAM,CAACvC,eAAe,CAACC,UAAU,EAAE0H,YAAY,CAAC,EAAE;MACxFC,MAAM,EAAErE,WAAW,CAACqB,OAAO,GAAG/E,eAAe,CAACuB,IAAI,CAAC,GAAGA,IAAI;MAC1DyG,IAAI,EAAEP,EAAE,GAAG,IAAI,GAAG;IACpB,CAAC,CAAC;IACF,CAACC,iBAAiB,GAAGzE,QAAQ,CAAC8B,OAAO,MAAM,IAAI,IAAI2C,iBAAiB,KAAK,KAAK,CAAC,IAAIA,iBAAiB,CAACpE,KAAK,CAAC,CAAC;EAC9G,CAAC;;EAED;EACA;AACF;AACA;AACA;AACA;EACE,IAAI2E,eAAe,GAAG,SAASA,eAAeA,CAAC7D,UAAU,EAAE;IACzD,IAAI8D,WAAW,GAAG/I,cAAc,CAACmF,YAAY,CAACmB,UAAU,CAAC,CAAC;IAC1D,IAAI0C,WAAW;IACf,IAAI,CAACD,WAAW,CAAC3C,KAAK,CAAC,CAAC,EAAE;MACxB;MACA;MACA4C,WAAW,GAAGzB,kBAAkB,CAACwB,WAAW,EAAE9D,UAAU,CAAC;IAC3D,CAAC,MAAM;MACL+D,WAAW,GAAGzB,kBAAkB,CAACrG,YAAY,EAAE+D,UAAU,CAAC;IAC5D;IACA,IAAI1D,KAAK,KAAKsD,SAAS,EAAE;MACvB;MACA2B,aAAa,CAACtF,YAAY,EAAE,KAAK,CAAC;IACpC,CAAC,MAAM,IAAI,CAAC8H,WAAW,CAAC5C,KAAK,CAAC,CAAC,EAAE;MAC/B;MACAI,aAAa,CAACwC,WAAW,EAAE,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C5E,aAAa,CAACuB,OAAO,GAAG,IAAI;EAC9B,CAAC;EACD,IAAIsD,SAAS,GAAG,SAASA,SAASA,CAACC,KAAK,EAAE;IACxC,IAAIC,GAAG,GAAGD,KAAK,CAACC,GAAG;MACjBC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IAC3BhF,aAAa,CAACuB,OAAO,GAAG,IAAI;IAC5BrB,WAAW,CAACqB,OAAO,GAAGyD,QAAQ;IAC9B,IAAID,GAAG,KAAK,OAAO,EAAE;MACnB,IAAI,CAAC9E,cAAc,CAACsB,OAAO,EAAE;QAC3BvB,aAAa,CAACuB,OAAO,GAAG,KAAK;MAC/B;MACAkD,eAAe,CAAC,KAAK,CAAC;MACtBxF,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAAC6F,KAAK,CAAC;IACzE;IACA,IAAIzG,QAAQ,KAAK,KAAK,EAAE;MACtB;IACF;;IAEA;IACA,IAAI,CAAC4B,cAAc,CAACsB,OAAO,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,CAAC,CAACM,QAAQ,CAACkD,GAAG,CAAC,EAAE;MACnFf,cAAc,CAACe,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,SAAS,CAAC;MACjDD,KAAK,CAACG,cAAc,CAAC,CAAC;IACxB;EACF,CAAC;EACD,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAC/BlF,aAAa,CAACuB,OAAO,GAAG,KAAK;IAC7BrB,WAAW,CAACqB,OAAO,GAAG,KAAK;EAC7B,CAAC;EACDlF,KAAK,CAAC8I,SAAS,CAAC,YAAY;IAC1B,IAAI5G,aAAa,IAAIuB,KAAK,EAAE;MAC1B,IAAIsF,OAAO,GAAG,SAASA,OAAOA,CAACN,KAAK,EAAE;QACpC;QACA;QACAd,cAAc,CAACc,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;QAChCP,KAAK,CAACG,cAAc,CAAC,CAAC;MACxB,CAAC;MACD,IAAI3D,KAAK,GAAG7B,QAAQ,CAAC8B,OAAO;MAC5B,IAAID,KAAK,EAAE;QACT;QACA;QACA;QACAA,KAAK,CAACgE,gBAAgB,CAAC,OAAO,EAAEF,OAAO,EAAE;UACvCG,OAAO,EAAE;QACX,CAAC,CAAC;QACF,OAAO,YAAY;UACjB,OAAOjE,KAAK,CAACkE,mBAAmB,CAAC,OAAO,EAAEJ,OAAO,CAAC;QACpD,CAAC;MACH;IACF;EACF,CAAC,CAAC;;EAEF;EACA,IAAIK,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAC7B,IAAIrG,YAAY,EAAE;MAChBqF,eAAe,CAAC,KAAK,CAAC;IACxB;IACA1E,QAAQ,CAAC,KAAK,CAAC;IACfC,aAAa,CAACuB,OAAO,GAAG,KAAK;EAC/B,CAAC;;EAED;EACA;EACArF,qBAAqB,CAAC,YAAY;IAChC,IAAI,CAACW,YAAY,CAACO,YAAY,CAAC,CAAC,EAAE;MAChC+E,aAAa,CAACtF,YAAY,EAAE,KAAK,CAAC;IACpC;EACF,CAAC,EAAE,CAACgC,SAAS,EAAED,SAAS,CAAC,CAAC;;EAE1B;EACA1C,qBAAqB,CAAC,YAAY;IAChC,IAAIkG,QAAQ,GAAGzG,cAAc,CAACuB,KAAK,CAAC;IACpCmD,eAAe,CAAC+B,QAAQ,CAAC;IACzB,IAAIsD,kBAAkB,GAAG/J,cAAc,CAACmF,YAAY,CAACmB,UAAU,CAAC,CAAC;;IAEjE;IACA;IACA,IAAI,CAACG,QAAQ,CAACiB,MAAM,CAACqC,kBAAkB,CAAC,IAAI,CAAC1F,aAAa,CAACuB,OAAO,IAAI3C,SAAS,EAAE;MAC/E;MACAuD,aAAa,CAACC,QAAQ,EAAEpC,aAAa,CAACuB,OAAO,CAAC;IAChD;EACF,CAAC,EAAE,CAACrE,KAAK,CAAC,CAAC;;EAEX;EACAhB,qBAAqB,CAAC,YAAY;IAChC,IAAI0C,SAAS,EAAE;MACbkE,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACb,UAAU,CAAC,CAAC;;EAEhB;EACA,OAAO,aAAa5F,KAAK,CAACsJ,aAAa,CAAC,KAAK,EAAE;IAC7CnI,GAAG,EAAE6B,MAAM;IACX3B,SAAS,EAAE1B,IAAI,CAACyB,SAAS,EAAEC,SAAS,EAAErC,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACmE,MAAM,CAAC/B,SAAS,EAAE,UAAU,CAAC,EAAEqC,KAAK,CAAC,EAAE,EAAE,CAACN,MAAM,CAAC/B,SAAS,EAAE,WAAW,CAAC,EAAEQ,QAAQ,CAAC,EAAE,EAAE,CAACuB,MAAM,CAAC/B,SAAS,EAAE,WAAW,CAAC,EAAES,QAAQ,CAAC,EAAE,EAAE,CAACsB,MAAM,CAAC/B,SAAS,EAAE,eAAe,CAAC,EAAEZ,YAAY,CAACkF,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAACvC,MAAM,CAAC/B,SAAS,EAAE,eAAe,CAAC,EAAE,CAACZ,YAAY,CAACO,YAAY,CAAC,CAAC,IAAI,CAAC6F,SAAS,CAACpG,YAAY,CAAC,CAAC,CAAC;IAC/Zc,KAAK,EAAEA,KAAK;IACZiI,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;MAC1B7F,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC;IACD0F,MAAM,EAAEA,MAAM;IACdZ,SAAS,EAAEA,SAAS;IACpBK,OAAO,EAAEA,OAAO;IAChBtB,kBAAkB,EAAEA,kBAAkB;IACtCC,gBAAgB,EAAEA,gBAAgB;IAClCe,aAAa,EAAEA;EACjB,CAAC,EAAEnG,QAAQ,IAAI,aAAapC,KAAK,CAACsJ,aAAa,CAACpJ,WAAW,EAAE;IAC3DkB,SAAS,EAAEA,SAAS;IACpBoI,MAAM,EAAE1H,SAAS;IACjB2H,QAAQ,EAAE1H,WAAW;IACrBoE,UAAU,EAAEA,UAAU;IACtBE,YAAY,EAAEA,YAAY;IAC1BxD,MAAM,EAAE8E;EACV,CAAC,CAAC,EAAE,aAAa3H,KAAK,CAACsJ,aAAa,CAAC,KAAK,EAAE;IAC1CjI,SAAS,EAAE,EAAE,CAAC8B,MAAM,CAACD,cAAc,EAAE,OAAO;EAC9C,CAAC,EAAE,aAAalD,KAAK,CAACsJ,aAAa,CAAC,OAAO,EAAEvK,QAAQ,CAAC;IACpD2K,YAAY,EAAE,KAAK;IACnBC,IAAI,EAAE,YAAY;IAClB,eAAe,EAAEpI,GAAG;IACpB,eAAe,EAAEC,GAAG;IACpB,eAAe,EAAEhB,YAAY,CAACO,YAAY,CAAC,CAAC,GAAG,IAAI,GAAGP,YAAY,CAACE,QAAQ,CAAC,CAAC;IAC7EgB,IAAI,EAAEA;EACR,CAAC,EAAEuB,UAAU,EAAE;IACb9B,GAAG,EAAEpB,UAAU,CAACqD,QAAQ,EAAEjC,GAAG,CAAC;IAC9BE,SAAS,EAAE6B,cAAc;IACzBrC,KAAK,EAAE+E,UAAU;IACjBlD,QAAQ,EAAE+E,eAAe;IACzB7F,QAAQ,EAAEA,QAAQ;IAClBC,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AACF,IAAI+H,WAAW,GAAG,aAAa5J,KAAK,CAACiB,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACpE,IAAIS,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IAC3BN,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBuI,gBAAgB,GAAG3I,KAAK,CAACE,SAAS;IAClCA,SAAS,GAAGyI,gBAAgB,KAAK,KAAK,CAAC,GAAG,iBAAiB,GAAGA,gBAAgB;IAC9EhJ,KAAK,GAAGK,KAAK,CAACL,KAAK;IACnBiJ,MAAM,GAAG5I,KAAK,CAAC4I,MAAM;IACrBC,MAAM,GAAG7I,KAAK,CAAC6I,MAAM;IACrBC,WAAW,GAAG9I,KAAK,CAAC8I,WAAW;IAC/BC,UAAU,GAAG/I,KAAK,CAAC+I,UAAU;IAC7B5I,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BgB,UAAU,GAAGnB,KAAK,CAACmB,UAAU;IAC7B6H,IAAI,GAAG/K,wBAAwB,CAAC+B,KAAK,EAAE7B,UAAU,CAAC;EACpD,IAAI8K,SAAS,GAAGnK,KAAK,CAACqD,MAAM,CAAC,IAAI,CAAC;EAClC,IAAI+G,iBAAiB,GAAGpK,KAAK,CAACqD,MAAM,CAAC,IAAI,CAAC;EAC1C,IAAIgH,aAAa,GAAGrK,KAAK,CAACqD,MAAM,CAAC,IAAI,CAAC;EACtC,IAAII,KAAK,GAAG,SAASA,KAAKA,CAAC6G,MAAM,EAAE;IACjC,IAAID,aAAa,CAACnF,OAAO,EAAE;MACzB9E,YAAY,CAACiK,aAAa,CAACnF,OAAO,EAAEoF,MAAM,CAAC;IAC7C;EACF,CAAC;EACDtK,KAAK,CAACuK,mBAAmB,CAACpJ,GAAG,EAAE,YAAY;IACzC,OAAOrB,WAAW,CAACuK,aAAa,CAACnF,OAAO,EAAE;MACxCzB,KAAK,EAAEA,KAAK;MACZ+G,aAAa,EAAEL,SAAS,CAACjF,OAAO,CAACsF,aAAa,IAAIJ,iBAAiB,CAAClF;IACtE,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO,aAAalF,KAAK,CAACsJ,aAAa,CAAC1J,SAAS,EAAE;IACjDyB,SAAS,EAAEA,SAAS;IACpBjB,YAAY,EAAEqD,KAAK;IACnBrC,SAAS,EAAEA,SAAS;IACpBP,KAAK,EAAEA,KAAK;IACZe,QAAQ,EAAEA,QAAQ;IAClBN,KAAK,EAAEA,KAAK;IACZwI,MAAM,EAAEA,MAAM;IACdC,MAAM,EAAEA,MAAM;IACdE,UAAU,EAAEA,UAAU;IACtBD,WAAW,EAAEA,WAAW;IACxB3H,UAAU,EAAEA,UAAU;IACtBoI,UAAU,EAAE;MACVC,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE,KAAK;MACnBC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE;IACd,CAAC;IACD1J,GAAG,EAAEgJ;EACP,CAAC,EAAE,aAAanK,KAAK,CAACsJ,aAAa,CAACtI,mBAAmB,EAAEjC,QAAQ,CAAC;IAChEqC,SAAS,EAAEA,SAAS;IACpBQ,QAAQ,EAAEA,QAAQ;IAClBT,GAAG,EAAEkJ,aAAa;IAClBrH,MAAM,EAAEoH,iBAAiB;IACzB/I,SAAS,EAAEgB,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC4C;EAChF,CAAC,EAAEiF,IAAI,CAAC,CAAC,CAAC;AACZ,CAAC,CAAC;AACF,IAAIY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCpB,WAAW,CAACqB,WAAW,GAAG,aAAa;AACzC;AACA,eAAerB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}