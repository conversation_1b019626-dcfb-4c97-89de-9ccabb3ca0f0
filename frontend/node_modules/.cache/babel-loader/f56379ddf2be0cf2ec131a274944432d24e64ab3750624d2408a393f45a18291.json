{"ast": null, "code": "import * as React from 'react';\nimport { FormContext } from '../context';\nexport default function useFormInstance() {\n  const {\n    form\n  } = React.useContext(FormContext);\n  return form;\n}", "map": {"version": 3, "names": ["React", "FormContext", "useFormInstance", "form", "useContext"], "sources": ["/Users/<USER>/Desktop/code/system/frontend/node_modules/antd/es/form/hooks/useFormInstance.js"], "sourcesContent": ["import * as React from 'react';\nimport { FormContext } from '../context';\nexport default function useFormInstance() {\n  const {\n    form\n  } = React.useContext(FormContext);\n  return form;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,YAAY;AACxC,eAAe,SAASC,eAAeA,CAAA,EAAG;EACxC,MAAM;IACJC;EACF,CAAC,GAAGH,KAAK,CAACI,UAAU,CAACH,WAAW,CAAC;EACjC,OAAOE,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}