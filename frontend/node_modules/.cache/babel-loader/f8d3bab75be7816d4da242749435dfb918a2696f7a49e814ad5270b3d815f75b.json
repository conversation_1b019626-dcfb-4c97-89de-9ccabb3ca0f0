{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nconst StatisticNumber = props => {\n  const {\n    value,\n    formatter,\n    precision,\n    decimalSeparator,\n    groupSeparator = '',\n    prefixCls\n  } = props;\n  let valueNode;\n  if (typeof formatter === 'function') {\n    // Customize formatter\n    valueNode = formatter(value);\n  } else {\n    // Internal formatter\n    const val = String(value);\n    const cells = val.match(/^(-?)(\\d*)(\\.(\\d+))?$/);\n    // Process if illegal number\n    if (!cells || val === '-') {\n      valueNode = val;\n    } else {\n      const negative = cells[1];\n      let int = cells[2] || '0';\n      let decimal = cells[4] || '';\n      int = int.replace(/\\B(?=(\\d{3})+(?!\\d))/g, groupSeparator);\n      if (typeof precision === 'number') {\n        decimal = decimal.padEnd(precision, '0').slice(0, precision > 0 ? precision : 0);\n      }\n      if (decimal) {\n        decimal = `${decimalSeparator}${decimal}`;\n      }\n      valueNode = [/*#__PURE__*/React.createElement(\"span\", {\n        key: \"int\",\n        className: `${prefixCls}-content-value-int`\n      }, negative, int), decimal && (/*#__PURE__*/React.createElement(\"span\", {\n        key: \"decimal\",\n        className: `${prefixCls}-content-value-decimal`\n      }, decimal))];\n    }\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-content-value`\n  }, valueNode);\n};\nexport default StatisticNumber;", "map": {"version": 3, "names": ["React", "StatisticNumber", "props", "value", "formatter", "precision", "decimalSeparator", "groupSeparator", "prefixCls", "valueNode", "val", "String", "cells", "match", "negative", "int", "decimal", "replace", "padEnd", "slice", "createElement", "key", "className"], "sources": ["/Users/<USER>/Desktop/code/system/frontend/node_modules/antd/es/statistic/Number.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nconst StatisticNumber = props => {\n  const {\n    value,\n    formatter,\n    precision,\n    decimalSeparator,\n    groupSeparator = '',\n    prefixCls\n  } = props;\n  let valueNode;\n  if (typeof formatter === 'function') {\n    // Customize formatter\n    valueNode = formatter(value);\n  } else {\n    // Internal formatter\n    const val = String(value);\n    const cells = val.match(/^(-?)(\\d*)(\\.(\\d+))?$/);\n    // Process if illegal number\n    if (!cells || val === '-') {\n      valueNode = val;\n    } else {\n      const negative = cells[1];\n      let int = cells[2] || '0';\n      let decimal = cells[4] || '';\n      int = int.replace(/\\B(?=(\\d{3})+(?!\\d))/g, groupSeparator);\n      if (typeof precision === 'number') {\n        decimal = decimal.padEnd(precision, '0').slice(0, precision > 0 ? precision : 0);\n      }\n      if (decimal) {\n        decimal = `${decimalSeparator}${decimal}`;\n      }\n      valueNode = [/*#__PURE__*/React.createElement(\"span\", {\n        key: \"int\",\n        className: `${prefixCls}-content-value-int`\n      }, negative, int), decimal && (/*#__PURE__*/React.createElement(\"span\", {\n        key: \"decimal\",\n        className: `${prefixCls}-content-value-decimal`\n      }, decimal))];\n    }\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-content-value`\n  }, valueNode);\n};\nexport default StatisticNumber;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,eAAe,GAAGC,KAAK,IAAI;EAC/B,MAAM;IACJC,KAAK;IACLC,SAAS;IACTC,SAAS;IACTC,gBAAgB;IAChBC,cAAc,GAAG,EAAE;IACnBC;EACF,CAAC,GAAGN,KAAK;EACT,IAAIO,SAAS;EACb,IAAI,OAAOL,SAAS,KAAK,UAAU,EAAE;IACnC;IACAK,SAAS,GAAGL,SAAS,CAACD,KAAK,CAAC;EAC9B,CAAC,MAAM;IACL;IACA,MAAMO,GAAG,GAAGC,MAAM,CAACR,KAAK,CAAC;IACzB,MAAMS,KAAK,GAAGF,GAAG,CAACG,KAAK,CAAC,uBAAuB,CAAC;IAChD;IACA,IAAI,CAACD,KAAK,IAAIF,GAAG,KAAK,GAAG,EAAE;MACzBD,SAAS,GAAGC,GAAG;IACjB,CAAC,MAAM;MACL,MAAMI,QAAQ,GAAGF,KAAK,CAAC,CAAC,CAAC;MACzB,IAAIG,GAAG,GAAGH,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG;MACzB,IAAII,OAAO,GAAGJ,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;MAC5BG,GAAG,GAAGA,GAAG,CAACE,OAAO,CAAC,uBAAuB,EAAEV,cAAc,CAAC;MAC1D,IAAI,OAAOF,SAAS,KAAK,QAAQ,EAAE;QACjCW,OAAO,GAAGA,OAAO,CAACE,MAAM,CAACb,SAAS,EAAE,GAAG,CAAC,CAACc,KAAK,CAAC,CAAC,EAAEd,SAAS,GAAG,CAAC,GAAGA,SAAS,GAAG,CAAC,CAAC;MAClF;MACA,IAAIW,OAAO,EAAE;QACXA,OAAO,GAAG,GAAGV,gBAAgB,GAAGU,OAAO,EAAE;MAC3C;MACAP,SAAS,GAAG,CAAC,aAAaT,KAAK,CAACoB,aAAa,CAAC,MAAM,EAAE;QACpDC,GAAG,EAAE,KAAK;QACVC,SAAS,EAAE,GAAGd,SAAS;MACzB,CAAC,EAAEM,QAAQ,EAAEC,GAAG,CAAC,EAAEC,OAAO,KAAK,aAAahB,KAAK,CAACoB,aAAa,CAAC,MAAM,EAAE;QACtEC,GAAG,EAAE,SAAS;QACdC,SAAS,EAAE,GAAGd,SAAS;MACzB,CAAC,EAAEQ,OAAO,CAAC,CAAC,CAAC;IACf;EACF;EACA,OAAO,aAAahB,KAAK,CAACoB,aAAa,CAAC,MAAM,EAAE;IAC9CE,SAAS,EAAE,GAAGd,SAAS;EACzB,CAAC,EAAEC,SAAS,CAAC;AACf,CAAC;AACD,eAAeR,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}