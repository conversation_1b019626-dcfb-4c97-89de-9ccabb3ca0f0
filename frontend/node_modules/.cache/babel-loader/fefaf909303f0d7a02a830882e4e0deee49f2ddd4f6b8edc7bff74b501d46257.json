{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nvar calcThumbStyle = function calcThumbStyle(targetElement, vertical) {\n  if (!targetElement) return null;\n  var style = {\n    left: targetElement.offsetLeft,\n    right: targetElement.parentElement.clientWidth - targetElement.clientWidth - targetElement.offsetLeft,\n    width: targetElement.clientWidth,\n    top: targetElement.offsetTop,\n    bottom: targetElement.parentElement.clientHeight - targetElement.clientHeight - targetElement.offsetTop,\n    height: targetElement.clientHeight\n  };\n  if (vertical) {\n    // Adjusts positioning and size for vertical layout by setting horizontal properties to 0 and using vertical properties from the style object.\n    return {\n      left: 0,\n      right: 0,\n      width: 0,\n      top: style.top,\n      bottom: style.bottom,\n      height: style.height\n    };\n  }\n  return {\n    left: style.left,\n    right: style.right,\n    width: style.width,\n    top: 0,\n    bottom: 0,\n    height: 0\n  };\n};\nvar toPX = function toPX(value) {\n  return value !== undefined ? \"\".concat(value, \"px\") : undefined;\n};\nexport default function MotionThumb(props) {\n  var prefixCls = props.prefixCls,\n    containerRef = props.containerRef,\n    value = props.value,\n    getValueIndex = props.getValueIndex,\n    motionName = props.motionName,\n    onMotionStart = props.onMotionStart,\n    onMotionEnd = props.onMotionEnd,\n    direction = props.direction,\n    _props$vertical = props.vertical,\n    vertical = _props$vertical === void 0 ? false : _props$vertical;\n  var thumbRef = React.useRef(null);\n  var _React$useState = React.useState(value),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    prevValue = _React$useState2[0],\n    setPrevValue = _React$useState2[1];\n\n  // =========================== Effect ===========================\n  var findValueElement = function findValueElement(val) {\n    var _containerRef$current;\n    var index = getValueIndex(val);\n    var ele = (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.querySelectorAll(\".\".concat(prefixCls, \"-item\"))[index];\n    return (ele === null || ele === void 0 ? void 0 : ele.offsetParent) && ele;\n  };\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    prevStyle = _React$useState4[0],\n    setPrevStyle = _React$useState4[1];\n  var _React$useState5 = React.useState(null),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    nextStyle = _React$useState6[0],\n    setNextStyle = _React$useState6[1];\n  useLayoutEffect(function () {\n    if (prevValue !== value) {\n      var prev = findValueElement(prevValue);\n      var next = findValueElement(value);\n      var calcPrevStyle = calcThumbStyle(prev, vertical);\n      var calcNextStyle = calcThumbStyle(next, vertical);\n      setPrevValue(value);\n      setPrevStyle(calcPrevStyle);\n      setNextStyle(calcNextStyle);\n      if (prev && next) {\n        onMotionStart();\n      } else {\n        onMotionEnd();\n      }\n    }\n  }, [value]);\n  var thumbStart = React.useMemo(function () {\n    if (vertical) {\n      var _prevStyle$top;\n      return toPX((_prevStyle$top = prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.top) !== null && _prevStyle$top !== void 0 ? _prevStyle$top : 0);\n    }\n    if (direction === 'rtl') {\n      return toPX(-(prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.right));\n    }\n    return toPX(prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.left);\n  }, [vertical, direction, prevStyle]);\n  var thumbActive = React.useMemo(function () {\n    if (vertical) {\n      var _nextStyle$top;\n      return toPX((_nextStyle$top = nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.top) !== null && _nextStyle$top !== void 0 ? _nextStyle$top : 0);\n    }\n    if (direction === 'rtl') {\n      return toPX(-(nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.right));\n    }\n    return toPX(nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.left);\n  }, [vertical, direction, nextStyle]);\n\n  // =========================== Motion ===========================\n  var onAppearStart = function onAppearStart() {\n    if (vertical) {\n      return {\n        transform: 'translateY(var(--thumb-start-top))',\n        height: 'var(--thumb-start-height)'\n      };\n    }\n    return {\n      transform: 'translateX(var(--thumb-start-left))',\n      width: 'var(--thumb-start-width)'\n    };\n  };\n  var onAppearActive = function onAppearActive() {\n    if (vertical) {\n      return {\n        transform: 'translateY(var(--thumb-active-top))',\n        height: 'var(--thumb-active-height)'\n      };\n    }\n    return {\n      transform: 'translateX(var(--thumb-active-left))',\n      width: 'var(--thumb-active-width)'\n    };\n  };\n  var onVisibleChanged = function onVisibleChanged() {\n    setPrevStyle(null);\n    setNextStyle(null);\n    onMotionEnd();\n  };\n\n  // =========================== Render ===========================\n  // No need motion when nothing exist in queue\n  if (!prevStyle || !nextStyle) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: true,\n    motionName: motionName,\n    motionAppear: true,\n    onAppearStart: onAppearStart,\n    onAppearActive: onAppearActive,\n    onVisibleChanged: onVisibleChanged\n  }, function (_ref, ref) {\n    var motionClassName = _ref.className,\n      motionStyle = _ref.style;\n    var mergedStyle = _objectSpread(_objectSpread({}, motionStyle), {}, {\n      '--thumb-start-left': thumbStart,\n      '--thumb-start-width': toPX(prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.width),\n      '--thumb-active-left': thumbActive,\n      '--thumb-active-width': toPX(nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.width),\n      '--thumb-start-top': thumbStart,\n      '--thumb-start-height': toPX(prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.height),\n      '--thumb-active-top': thumbActive,\n      '--thumb-active-height': toPX(nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.height)\n    });\n\n    // It's little ugly which should be refactor when @umi/test update to latest jsdom\n    var motionProps = {\n      ref: composeRef(thumbRef, ref),\n      style: mergedStyle,\n      className: classNames(\"\".concat(prefixCls, \"-thumb\"), motionClassName)\n    };\n    if (process.env.NODE_ENV === 'test') {\n      motionProps['data-test-style'] = JSON.stringify(mergedStyle);\n    }\n    return /*#__PURE__*/React.createElement(\"div\", motionProps);\n  });\n}", "map": {"version": 3, "names": ["_objectSpread", "_slicedToArray", "classNames", "CSSMotion", "useLayoutEffect", "composeRef", "React", "calcThumbStyle", "targetElement", "vertical", "style", "left", "offsetLeft", "right", "parentElement", "clientWidth", "width", "top", "offsetTop", "bottom", "clientHeight", "height", "toPX", "value", "undefined", "concat", "MotionThumb", "props", "prefixCls", "containerRef", "getValueIndex", "motionName", "onMotionStart", "onMotionEnd", "direction", "_props$vertical", "thumbRef", "useRef", "_React$useState", "useState", "_React$useState2", "prevValue", "setPrevValue", "findValueElement", "val", "_containerRef$current", "index", "ele", "current", "querySelectorAll", "offsetParent", "_React$useState3", "_React$useState4", "prevStyle", "setPrevStyle", "_React$useState5", "_React$useState6", "nextStyle", "setNextStyle", "prev", "next", "calcPrevStyle", "calcNextStyle", "thumbStart", "useMemo", "_prevStyle$top", "thumbActive", "_nextStyle$top", "onAppearStart", "transform", "onAppearActive", "onVisibleChanged", "createElement", "visible", "motionAppear", "_ref", "ref", "motionClassName", "className", "motionStyle", "mergedStyle", "motionProps", "process", "env", "NODE_ENV", "JSON", "stringify"], "sources": ["/Users/<USER>/Desktop/code/system/frontend/node_modules/rc-segmented/es/MotionThumb.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nvar calcThumbStyle = function calcThumbStyle(targetElement, vertical) {\n  if (!targetElement) return null;\n  var style = {\n    left: targetElement.offsetLeft,\n    right: targetElement.parentElement.clientWidth - targetElement.clientWidth - targetElement.offsetLeft,\n    width: targetElement.clientWidth,\n    top: targetElement.offsetTop,\n    bottom: targetElement.parentElement.clientHeight - targetElement.clientHeight - targetElement.offsetTop,\n    height: targetElement.clientHeight\n  };\n  if (vertical) {\n    // Adjusts positioning and size for vertical layout by setting horizontal properties to 0 and using vertical properties from the style object.\n    return {\n      left: 0,\n      right: 0,\n      width: 0,\n      top: style.top,\n      bottom: style.bottom,\n      height: style.height\n    };\n  }\n  return {\n    left: style.left,\n    right: style.right,\n    width: style.width,\n    top: 0,\n    bottom: 0,\n    height: 0\n  };\n};\nvar toPX = function toPX(value) {\n  return value !== undefined ? \"\".concat(value, \"px\") : undefined;\n};\nexport default function MotionThumb(props) {\n  var prefixCls = props.prefixCls,\n    containerRef = props.containerRef,\n    value = props.value,\n    getValueIndex = props.getValueIndex,\n    motionName = props.motionName,\n    onMotionStart = props.onMotionStart,\n    onMotionEnd = props.onMotionEnd,\n    direction = props.direction,\n    _props$vertical = props.vertical,\n    vertical = _props$vertical === void 0 ? false : _props$vertical;\n  var thumbRef = React.useRef(null);\n  var _React$useState = React.useState(value),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    prevValue = _React$useState2[0],\n    setPrevValue = _React$useState2[1];\n\n  // =========================== Effect ===========================\n  var findValueElement = function findValueElement(val) {\n    var _containerRef$current;\n    var index = getValueIndex(val);\n    var ele = (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.querySelectorAll(\".\".concat(prefixCls, \"-item\"))[index];\n    return (ele === null || ele === void 0 ? void 0 : ele.offsetParent) && ele;\n  };\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    prevStyle = _React$useState4[0],\n    setPrevStyle = _React$useState4[1];\n  var _React$useState5 = React.useState(null),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    nextStyle = _React$useState6[0],\n    setNextStyle = _React$useState6[1];\n  useLayoutEffect(function () {\n    if (prevValue !== value) {\n      var prev = findValueElement(prevValue);\n      var next = findValueElement(value);\n      var calcPrevStyle = calcThumbStyle(prev, vertical);\n      var calcNextStyle = calcThumbStyle(next, vertical);\n      setPrevValue(value);\n      setPrevStyle(calcPrevStyle);\n      setNextStyle(calcNextStyle);\n      if (prev && next) {\n        onMotionStart();\n      } else {\n        onMotionEnd();\n      }\n    }\n  }, [value]);\n  var thumbStart = React.useMemo(function () {\n    if (vertical) {\n      var _prevStyle$top;\n      return toPX((_prevStyle$top = prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.top) !== null && _prevStyle$top !== void 0 ? _prevStyle$top : 0);\n    }\n    if (direction === 'rtl') {\n      return toPX(-(prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.right));\n    }\n    return toPX(prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.left);\n  }, [vertical, direction, prevStyle]);\n  var thumbActive = React.useMemo(function () {\n    if (vertical) {\n      var _nextStyle$top;\n      return toPX((_nextStyle$top = nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.top) !== null && _nextStyle$top !== void 0 ? _nextStyle$top : 0);\n    }\n    if (direction === 'rtl') {\n      return toPX(-(nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.right));\n    }\n    return toPX(nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.left);\n  }, [vertical, direction, nextStyle]);\n\n  // =========================== Motion ===========================\n  var onAppearStart = function onAppearStart() {\n    if (vertical) {\n      return {\n        transform: 'translateY(var(--thumb-start-top))',\n        height: 'var(--thumb-start-height)'\n      };\n    }\n    return {\n      transform: 'translateX(var(--thumb-start-left))',\n      width: 'var(--thumb-start-width)'\n    };\n  };\n  var onAppearActive = function onAppearActive() {\n    if (vertical) {\n      return {\n        transform: 'translateY(var(--thumb-active-top))',\n        height: 'var(--thumb-active-height)'\n      };\n    }\n    return {\n      transform: 'translateX(var(--thumb-active-left))',\n      width: 'var(--thumb-active-width)'\n    };\n  };\n  var onVisibleChanged = function onVisibleChanged() {\n    setPrevStyle(null);\n    setNextStyle(null);\n    onMotionEnd();\n  };\n\n  // =========================== Render ===========================\n  // No need motion when nothing exist in queue\n  if (!prevStyle || !nextStyle) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: true,\n    motionName: motionName,\n    motionAppear: true,\n    onAppearStart: onAppearStart,\n    onAppearActive: onAppearActive,\n    onVisibleChanged: onVisibleChanged\n  }, function (_ref, ref) {\n    var motionClassName = _ref.className,\n      motionStyle = _ref.style;\n    var mergedStyle = _objectSpread(_objectSpread({}, motionStyle), {}, {\n      '--thumb-start-left': thumbStart,\n      '--thumb-start-width': toPX(prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.width),\n      '--thumb-active-left': thumbActive,\n      '--thumb-active-width': toPX(nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.width),\n      '--thumb-start-top': thumbStart,\n      '--thumb-start-height': toPX(prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.height),\n      '--thumb-active-top': thumbActive,\n      '--thumb-active-height': toPX(nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.height)\n    });\n\n    // It's little ugly which should be refactor when @umi/test update to latest jsdom\n    var motionProps = {\n      ref: composeRef(thumbRef, ref),\n      style: mergedStyle,\n      className: classNames(\"\".concat(prefixCls, \"-thumb\"), motionClassName)\n    };\n    if (process.env.NODE_ENV === 'test') {\n      motionProps['data-test-style'] = JSON.stringify(mergedStyle);\n    }\n    return /*#__PURE__*/React.createElement(\"div\", motionProps);\n  });\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,aAAa,EAAEC,QAAQ,EAAE;EACpE,IAAI,CAACD,aAAa,EAAE,OAAO,IAAI;EAC/B,IAAIE,KAAK,GAAG;IACVC,IAAI,EAAEH,aAAa,CAACI,UAAU;IAC9BC,KAAK,EAAEL,aAAa,CAACM,aAAa,CAACC,WAAW,GAAGP,aAAa,CAACO,WAAW,GAAGP,aAAa,CAACI,UAAU;IACrGI,KAAK,EAAER,aAAa,CAACO,WAAW;IAChCE,GAAG,EAAET,aAAa,CAACU,SAAS;IAC5BC,MAAM,EAAEX,aAAa,CAACM,aAAa,CAACM,YAAY,GAAGZ,aAAa,CAACY,YAAY,GAAGZ,aAAa,CAACU,SAAS;IACvGG,MAAM,EAAEb,aAAa,CAACY;EACxB,CAAC;EACD,IAAIX,QAAQ,EAAE;IACZ;IACA,OAAO;MACLE,IAAI,EAAE,CAAC;MACPE,KAAK,EAAE,CAAC;MACRG,KAAK,EAAE,CAAC;MACRC,GAAG,EAAEP,KAAK,CAACO,GAAG;MACdE,MAAM,EAAET,KAAK,CAACS,MAAM;MACpBE,MAAM,EAAEX,KAAK,CAACW;IAChB,CAAC;EACH;EACA,OAAO;IACLV,IAAI,EAAED,KAAK,CAACC,IAAI;IAChBE,KAAK,EAAEH,KAAK,CAACG,KAAK;IAClBG,KAAK,EAAEN,KAAK,CAACM,KAAK;IAClBC,GAAG,EAAE,CAAC;IACNE,MAAM,EAAE,CAAC;IACTE,MAAM,EAAE;EACV,CAAC;AACH,CAAC;AACD,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,KAAK,EAAE;EAC9B,OAAOA,KAAK,KAAKC,SAAS,GAAG,EAAE,CAACC,MAAM,CAACF,KAAK,EAAE,IAAI,CAAC,GAAGC,SAAS;AACjE,CAAC;AACD,eAAe,SAASE,WAAWA,CAACC,KAAK,EAAE;EACzC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACjCN,KAAK,GAAGI,KAAK,CAACJ,KAAK;IACnBO,aAAa,GAAGH,KAAK,CAACG,aAAa;IACnCC,UAAU,GAAGJ,KAAK,CAACI,UAAU;IAC7BC,aAAa,GAAGL,KAAK,CAACK,aAAa;IACnCC,WAAW,GAAGN,KAAK,CAACM,WAAW;IAC/BC,SAAS,GAAGP,KAAK,CAACO,SAAS;IAC3BC,eAAe,GAAGR,KAAK,CAAClB,QAAQ;IAChCA,QAAQ,GAAG0B,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,eAAe;EACjE,IAAIC,QAAQ,GAAG9B,KAAK,CAAC+B,MAAM,CAAC,IAAI,CAAC;EACjC,IAAIC,eAAe,GAAGhC,KAAK,CAACiC,QAAQ,CAAChB,KAAK,CAAC;IACzCiB,gBAAgB,GAAGvC,cAAc,CAACqC,eAAe,EAAE,CAAC,CAAC;IACrDG,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;EAEpC;EACA,IAAIG,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,GAAG,EAAE;IACpD,IAAIC,qBAAqB;IACzB,IAAIC,KAAK,GAAGhB,aAAa,CAACc,GAAG,CAAC;IAC9B,IAAIG,GAAG,GAAG,CAACF,qBAAqB,GAAGhB,YAAY,CAACmB,OAAO,MAAM,IAAI,IAAIH,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACI,gBAAgB,CAAC,GAAG,CAACxB,MAAM,CAACG,SAAS,EAAE,OAAO,CAAC,CAAC,CAACkB,KAAK,CAAC;IAC9L,OAAO,CAACC,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACG,YAAY,KAAKH,GAAG;EAC5E,CAAC;EACD,IAAII,gBAAgB,GAAG7C,KAAK,CAACiC,QAAQ,CAAC,IAAI,CAAC;IACzCa,gBAAgB,GAAGnD,cAAc,CAACkD,gBAAgB,EAAE,CAAC,CAAC;IACtDE,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACpC,IAAIG,gBAAgB,GAAGjD,KAAK,CAACiC,QAAQ,CAAC,IAAI,CAAC;IACzCiB,gBAAgB,GAAGvD,cAAc,CAACsD,gBAAgB,EAAE,CAAC,CAAC;IACtDE,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACpCpD,eAAe,CAAC,YAAY;IAC1B,IAAIqC,SAAS,KAAKlB,KAAK,EAAE;MACvB,IAAIoC,IAAI,GAAGhB,gBAAgB,CAACF,SAAS,CAAC;MACtC,IAAImB,IAAI,GAAGjB,gBAAgB,CAACpB,KAAK,CAAC;MAClC,IAAIsC,aAAa,GAAGtD,cAAc,CAACoD,IAAI,EAAElD,QAAQ,CAAC;MAClD,IAAIqD,aAAa,GAAGvD,cAAc,CAACqD,IAAI,EAAEnD,QAAQ,CAAC;MAClDiC,YAAY,CAACnB,KAAK,CAAC;MACnB+B,YAAY,CAACO,aAAa,CAAC;MAC3BH,YAAY,CAACI,aAAa,CAAC;MAC3B,IAAIH,IAAI,IAAIC,IAAI,EAAE;QAChB5B,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACLC,WAAW,CAAC,CAAC;MACf;IACF;EACF,CAAC,EAAE,CAACV,KAAK,CAAC,CAAC;EACX,IAAIwC,UAAU,GAAGzD,KAAK,CAAC0D,OAAO,CAAC,YAAY;IACzC,IAAIvD,QAAQ,EAAE;MACZ,IAAIwD,cAAc;MAClB,OAAO3C,IAAI,CAAC,CAAC2C,cAAc,GAAGZ,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACpC,GAAG,MAAM,IAAI,IAAIgD,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAG,CAAC,CAAC;IAChK;IACA,IAAI/B,SAAS,KAAK,KAAK,EAAE;MACvB,OAAOZ,IAAI,CAAC,EAAE+B,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACxC,KAAK,CAAC,CAAC;IACvF;IACA,OAAOS,IAAI,CAAC+B,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC1C,IAAI,CAAC;EACnF,CAAC,EAAE,CAACF,QAAQ,EAAEyB,SAAS,EAAEmB,SAAS,CAAC,CAAC;EACpC,IAAIa,WAAW,GAAG5D,KAAK,CAAC0D,OAAO,CAAC,YAAY;IAC1C,IAAIvD,QAAQ,EAAE;MACZ,IAAI0D,cAAc;MAClB,OAAO7C,IAAI,CAAC,CAAC6C,cAAc,GAAGV,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACxC,GAAG,MAAM,IAAI,IAAIkD,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAG,CAAC,CAAC;IAChK;IACA,IAAIjC,SAAS,KAAK,KAAK,EAAE;MACvB,OAAOZ,IAAI,CAAC,EAAEmC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC5C,KAAK,CAAC,CAAC;IACvF;IACA,OAAOS,IAAI,CAACmC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC9C,IAAI,CAAC;EACnF,CAAC,EAAE,CAACF,QAAQ,EAAEyB,SAAS,EAAEuB,SAAS,CAAC,CAAC;;EAEpC;EACA,IAAIW,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C,IAAI3D,QAAQ,EAAE;MACZ,OAAO;QACL4D,SAAS,EAAE,oCAAoC;QAC/ChD,MAAM,EAAE;MACV,CAAC;IACH;IACA,OAAO;MACLgD,SAAS,EAAE,qCAAqC;MAChDrD,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EACD,IAAIsD,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;IAC7C,IAAI7D,QAAQ,EAAE;MACZ,OAAO;QACL4D,SAAS,EAAE,qCAAqC;QAChDhD,MAAM,EAAE;MACV,CAAC;IACH;IACA,OAAO;MACLgD,SAAS,EAAE,sCAAsC;MACjDrD,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EACD,IAAIuD,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjDjB,YAAY,CAAC,IAAI,CAAC;IAClBI,YAAY,CAAC,IAAI,CAAC;IAClBzB,WAAW,CAAC,CAAC;EACf,CAAC;;EAED;EACA;EACA,IAAI,CAACoB,SAAS,IAAI,CAACI,SAAS,EAAE;IAC5B,OAAO,IAAI;EACb;EACA,OAAO,aAAanD,KAAK,CAACkE,aAAa,CAACrE,SAAS,EAAE;IACjDsE,OAAO,EAAE,IAAI;IACb1C,UAAU,EAAEA,UAAU;IACtB2C,YAAY,EAAE,IAAI;IAClBN,aAAa,EAAEA,aAAa;IAC5BE,cAAc,EAAEA,cAAc;IAC9BC,gBAAgB,EAAEA;EACpB,CAAC,EAAE,UAAUI,IAAI,EAAEC,GAAG,EAAE;IACtB,IAAIC,eAAe,GAAGF,IAAI,CAACG,SAAS;MAClCC,WAAW,GAAGJ,IAAI,CAACjE,KAAK;IAC1B,IAAIsE,WAAW,GAAGhF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+E,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;MAClE,oBAAoB,EAAEhB,UAAU;MAChC,qBAAqB,EAAEzC,IAAI,CAAC+B,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACrC,KAAK,CAAC;MAClG,qBAAqB,EAAEkD,WAAW;MAClC,sBAAsB,EAAE5C,IAAI,CAACmC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACzC,KAAK,CAAC;MACnG,mBAAmB,EAAE+C,UAAU;MAC/B,sBAAsB,EAAEzC,IAAI,CAAC+B,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAChC,MAAM,CAAC;MACpG,oBAAoB,EAAE6C,WAAW;MACjC,uBAAuB,EAAE5C,IAAI,CAACmC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACpC,MAAM;IACtG,CAAC,CAAC;;IAEF;IACA,IAAI4D,WAAW,GAAG;MAChBL,GAAG,EAAEvE,UAAU,CAAC+B,QAAQ,EAAEwC,GAAG,CAAC;MAC9BlE,KAAK,EAAEsE,WAAW;MAClBF,SAAS,EAAE5E,UAAU,CAAC,EAAE,CAACuB,MAAM,CAACG,SAAS,EAAE,QAAQ,CAAC,EAAEiD,eAAe;IACvE,CAAC;IACD,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;MACnCH,WAAW,CAAC,iBAAiB,CAAC,GAAGI,IAAI,CAACC,SAAS,CAACN,WAAW,CAAC;IAC9D;IACA,OAAO,aAAa1E,KAAK,CAACkE,aAAa,CAAC,KAAK,EAAES,WAAW,CAAC;EAC7D,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}