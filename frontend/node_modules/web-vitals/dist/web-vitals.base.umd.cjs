!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((e="undefined"!=typeof globalThis?globalThis:e||self).webVitals=e.webVitals||{})}(this,(function(e){"use strict";var n,t=-1,i=function(e){addEventListener("pageshow",(function(n){n.persisted&&(t=n.timeStamp,e(n))}),!0)},r=function(){return window.performance&&(performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]||function(){var e=performance.timing,n=performance.navigation.type,t={entryType:"navigation",startTime:0,type:2==n?"back_forward":1===n?"reload":"navigate"};for(var i in e)"navigationStart"!==i&&"toJSON"!==i&&(t[i]=Math.max(e[i]-e.navigationStart,0));return t}())},o=function(){var e=r();return e&&e.activationStart||0},a=function(e,n){var i=r(),a="navigate";t>=0?a="back-forward-cache":i&&(document.prerendering||o()>0?a="prerender":document.wasDiscarded?a="restore":i.type&&(a=i.type.replace(/_/g,"-")));return{name:e,value:void 0===n?-1:n,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:a}},u=function(e,n,t){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var i=new PerformanceObserver((function(e){Promise.resolve().then((function(){n(e.getEntries())}))}));return i.observe(Object.assign({type:e,buffered:!0},t||{})),i}}catch(e){}},c=function(e,n,t,i){var r,o;return function(a){n.value>=0&&(a||i)&&((o=n.value-(r||0))||void 0===r)&&(r=n.value,n.delta=o,n.rating=function(e,n){return e>n[1]?"poor":e>n[0]?"needs-improvement":"good"}(n.value,t),e(n))}},s=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},f=function(e){var n=function(n){"pagehide"!==n.type&&"hidden"!==document.visibilityState||e(n)};addEventListener("visibilitychange",n,!0),addEventListener("pagehide",n,!0)},d=function(e){var n=!1;return function(t){n||(e(t),n=!0)}},l=-1,p=function(e){"hidden"===document.visibilityState&&l>-1&&(l="visibilitychange"===e.type?e.timeStamp:0,m())},v=function(){addEventListener("visibilitychange",p,!0),addEventListener("prerenderingchange",p,!0)},m=function(){removeEventListener("visibilitychange",p,!0),removeEventListener("prerenderingchange",p,!0)},h=function(){return l<0&&((l=window.webVitals.firstHiddenTime)===1/0&&v(),i((function(){setTimeout((function(){l="hidden"!==document.visibilityState||document.prerendering?1/0:0,v()}),0)}))),{get firstHiddenTime(){return l}}},g=function(e){document.prerendering?addEventListener("prerenderingchange",(function(){return e()}),!0):e()},T=[1800,3e3],y=function(e,n){n=n||{},g((function(){var t,r=h(),f=a("FCP"),d=u("paint",(function(e){e.forEach((function(e){"first-contentful-paint"===e.name&&(d.disconnect(),e.startTime<r.firstHiddenTime&&(f.value=Math.max(e.startTime-o(),0),f.entries.push(e),t(!0)))}))}));d&&(t=c(e,f,T,n.reportAllChanges),i((function(i){f=a("FCP"),t=c(e,f,T,n.reportAllChanges),s((function(){f.value=performance.now()-i.timeStamp,t(!0)}))})))}))},b=[.1,.25],w=function(e,n){n=n||{},y(d((function(){var t,r=a("CLS",0),o=0,d=[],l=function(e){e.forEach((function(e){if(!e.hadRecentInput){var n=d[0],t=d[d.length-1];o&&e.startTime-t.startTime<1e3&&e.startTime-n.startTime<5e3?(o+=e.value,d.push(e)):(o=e.value,d=[e])}})),o>r.value&&(r.value=o,r.entries=d,t())},p=u("layout-shift",l);p&&(t=c(e,r,b,n.reportAllChanges),f((function(){l(p.takeRecords()),t(!0)})),i((function(){o=0,r=a("CLS",0),t=c(e,r,b,n.reportAllChanges),s((function(){return t()}))})),setTimeout(t,0))})))},C=[100,300],E=function(e,n){n=n||{},g((function(){var t,r=h(),o=a("FID"),s=function(e){e.startTime<r.firstHiddenTime&&(o.value=e.processingStart-e.startTime,o.entries.push(e),t(!0))},l=function(e){e.forEach(s)},p=u("first-input",l);t=c(e,o,C,n.reportAllChanges),p&&f(d((function(){l(p.takeRecords()),p.disconnect()}))),console.warn('The web-vitals "base+polyfill" build is deprecated. See: https://bit.ly/3aqzsGm'),p||window.webVitals.firstInputPolyfill(s),i((function(){o=a("FID"),t=c(e,o,C,n.reportAllChanges),window.webVitals.resetFirstInputPolyfill(),window.webVitals.firstInputPolyfill(s)}))}))},P=0,I=1/0,L=0,S=function(e){e.forEach((function(e){e.interactionId&&(I=Math.min(I,e.interactionId),L=Math.max(L,e.interactionId),P=L?(L-I)/7+1:0)}))},F=function(){return n?P:performance.interactionCount||0},A=function(){"interactionCount"in performance||n||(n=u("event",S,{type:"event",buffered:!0,durationThreshold:0}))},M=[200,500],x=0,k=function(){return F()-x},B=[],D={},N=function(e){var n=B[B.length-1],t=D[e.interactionId];if(t||B.length<10||e.duration>n.latency){if(t)t.entries.push(e),t.latency=Math.max(t.latency,e.duration);else{var i={id:e.interactionId,latency:e.duration,entries:[e]};D[i.id]=i,B.push(i)}B.sort((function(e,n){return n.latency-e.latency})),B.splice(10).forEach((function(e){delete D[e.id]}))}},V=function(e,n){n=n||{},g((function(){var t;A();var r,o=a("INP"),s=function(e){e.forEach((function(e){(e.interactionId&&N(e),"first-input"===e.entryType)&&(!B.some((function(n){return n.entries.some((function(n){return e.duration===n.duration&&e.startTime===n.startTime}))}))&&N(e))}));var n,t=(n=Math.min(B.length-1,Math.floor(k()/50)),B[n]);t&&t.latency!==o.value&&(o.value=t.latency,o.entries=t.entries,r())},d=u("event",s,{durationThreshold:null!==(t=n.durationThreshold)&&void 0!==t?t:40});r=c(e,o,M,n.reportAllChanges),d&&("PerformanceEventTiming"in window&&"interactionId"in PerformanceEventTiming.prototype&&d.observe({type:"first-input",buffered:!0}),f((function(){s(d.takeRecords()),o.value<0&&k()>0&&(o.value=0,o.entries=[]),r(!0)})),i((function(){B=[],x=F(),o=a("INP"),r=c(e,o,M,n.reportAllChanges)})))}))},H=[2500,4e3],R={},O=function(e,n){n=n||{},g((function(){var t,r=h(),l=a("LCP"),p=function(e){var n=e[e.length-1];n&&n.startTime<r.firstHiddenTime&&(l.value=Math.max(n.startTime-o(),0),l.entries=[n],t())},v=u("largest-contentful-paint",p);if(v){t=c(e,l,H,n.reportAllChanges);var m=d((function(){R[l.id]||(p(v.takeRecords()),v.disconnect(),R[l.id]=!0,t(!0))}));["keydown","click"].forEach((function(e){addEventListener(e,(function(){return setTimeout(m,0)}),!0)})),f(m),i((function(i){l=a("LCP"),t=c(e,l,H,n.reportAllChanges),s((function(){l.value=performance.now()-i.timeStamp,R[l.id]=!0,t(!0)}))}))}}))},q=[800,1800],j=function e(n){document.prerendering?g((function(){return e(n)})):"complete"!==document.readyState?addEventListener("load",(function(){return e(n)}),!0):setTimeout(n,0)},_=function(e,n){n=n||{};var t=a("TTFB"),u=c(e,t,q,n.reportAllChanges);j((function(){var s=r();if(s){var f=s.responseStart;if(f<=0||f>performance.now())return;t.value=Math.max(f-o(),0),t.entries=[s],u(!0),i((function(){t=a("TTFB",0),(u=c(e,t,q,n.reportAllChanges))(!0)}))}}))};e.CLSThresholds=b,e.FCPThresholds=T,e.FIDThresholds=C,e.INPThresholds=M,e.LCPThresholds=H,e.TTFBThresholds=q,e.getCLS=w,e.getFCP=y,e.getFID=E,e.getINP=V,e.getLCP=O,e.getTTFB=_,e.onCLS=w,e.onFCP=y,e.onFID=E,e.onINP=V,e.onLCP=O,e.onTTFB=_}));
