!function(e){"use strict";var n,t=-1,i=function(e){addEventListener("pageshow",(function(n){n.persisted&&(t=n.timeStamp,e(n))}),!0)},r=function(){return window.performance&&(performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]||function(){var e=performance.timing,n=performance.navigation.type,t={entryType:"navigation",startTime:0,type:2==n?"back_forward":1===n?"reload":"navigate"};for(var i in e)"navigationStart"!==i&&"toJSON"!==i&&(t[i]=Math.max(e[i]-e.navigationStart,0));return t}())},o=function(){var e=r();return e&&e.activationStart||0},a=function(e,n){var i=r(),a="navigate";t>=0?a="back-forward-cache":i&&(document.prerendering||o()>0?a="prerender":document.wasDiscarded?a="restore":i.type&&(a=i.type.replace(/_/g,"-")));return{name:e,value:void 0===n?-1:n,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:a}},c=function(e,n,t){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var i=new PerformanceObserver((function(e){Promise.resolve().then((function(){n(e.getEntries())}))}));return i.observe(Object.assign({type:e,buffered:!0},t||{})),i}}catch(e){}},u=function(e,n,t,i){var r,o;return function(a){n.value>=0&&(a||i)&&((o=n.value-(r||0))||void 0===r)&&(r=n.value,n.delta=o,n.rating=function(e,n){return e>n[1]?"poor":e>n[0]?"needs-improvement":"good"}(n.value,t),e(n))}},s=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},f=function(e){var n=function(n){"pagehide"!==n.type&&"hidden"!==document.visibilityState||e(n)};addEventListener("visibilitychange",n,!0),addEventListener("pagehide",n,!0)},d=function(e){var n=!1;return function(t){n||(e(t),n=!0)}},l=-1,v=function(e){"hidden"===document.visibilityState&&l>-1&&(l="visibilitychange"===e.type?e.timeStamp:0,h())},p=function(){addEventListener("visibilitychange",v,!0),addEventListener("prerenderingchange",v,!0)},h=function(){removeEventListener("visibilitychange",v,!0),removeEventListener("prerenderingchange",v,!0)},m=function(){return l<0&&((l=window.webVitals.firstHiddenTime)===1/0&&p(),i((function(){setTimeout((function(){l="hidden"!==document.visibilityState||document.prerendering?1/0:0,p()}),0)}))),{get firstHiddenTime(){return l}}},g=function(e){document.prerendering?addEventListener("prerenderingchange",(function(){return e()}),!0):e()},T=[1800,3e3],y=function(e,n){n=n||{},g((function(){var t,r=m(),f=a("FCP"),d=c("paint",(function(e){e.forEach((function(e){"first-contentful-paint"===e.name&&(d.disconnect(),e.startTime<r.firstHiddenTime&&(f.value=Math.max(e.startTime-o(),0),f.entries.push(e),t(!0)))}))}));d&&(t=u(e,f,T,n.reportAllChanges),i((function(i){f=a("FCP"),t=u(e,f,T,n.reportAllChanges),s((function(){f.value=performance.now()-i.timeStamp,t(!0)}))})))}))},w=[.1,.25],C=function(e,n){n=n||{},y(d((function(){var t,r=a("CLS",0),o=0,d=[],l=function(e){e.forEach((function(e){if(!e.hadRecentInput){var n=d[0],t=d[d.length-1];o&&e.startTime-t.startTime<1e3&&e.startTime-n.startTime<5e3?(o+=e.value,d.push(e)):(o=e.value,d=[e])}})),o>r.value&&(r.value=o,r.entries=d,t())},v=c("layout-shift",l);v&&(t=u(e,r,w,n.reportAllChanges),f((function(){l(v.takeRecords()),t(!0)})),i((function(){o=0,r=a("CLS",0),t=u(e,r,w,n.reportAllChanges),s((function(){return t()}))})),setTimeout(t,0))})))},b=[100,300],E=function(e,n){n=n||{},g((function(){var t,r=m(),o=a("FID"),s=function(e){e.startTime<r.firstHiddenTime&&(o.value=e.processingStart-e.startTime,o.entries.push(e),t(!0))},l=function(e){e.forEach(s)},v=c("first-input",l);t=u(e,o,b,n.reportAllChanges),v&&f(d((function(){l(v.takeRecords()),v.disconnect()}))),console.warn('The web-vitals "base+polyfill" build is deprecated. See: https://bit.ly/3aqzsGm'),v||window.webVitals.firstInputPolyfill(s),i((function(){o=a("FID"),t=u(e,o,b,n.reportAllChanges),window.webVitals.resetFirstInputPolyfill(),window.webVitals.firstInputPolyfill(s)}))}))},P=0,I=1/0,L=0,S=function(e){e.forEach((function(e){e.interactionId&&(I=Math.min(I,e.interactionId),L=Math.max(L,e.interactionId),P=L?(L-I)/7+1:0)}))},F=function(){return n?P:performance.interactionCount||0},A=function(){"interactionCount"in performance||n||(n=c("event",S,{type:"event",buffered:!0,durationThreshold:0}))},M=[200,500],k=0,B=function(){return F()-k},D=[],x={},N=function(e){var n=D[D.length-1],t=x[e.interactionId];if(t||D.length<10||e.duration>n.latency){if(t)t.entries.push(e),t.latency=Math.max(t.latency,e.duration);else{var i={id:e.interactionId,latency:e.duration,entries:[e]};x[i.id]=i,D.push(i)}D.sort((function(e,n){return n.latency-e.latency})),D.splice(10).forEach((function(e){delete x[e.id]}))}},V=function(e,n){n=n||{},g((function(){var t;A();var r,o=a("INP"),s=function(e){e.forEach((function(e){(e.interactionId&&N(e),"first-input"===e.entryType)&&(!D.some((function(n){return n.entries.some((function(n){return e.duration===n.duration&&e.startTime===n.startTime}))}))&&N(e))}));var n,t=(n=Math.min(D.length-1,Math.floor(B()/50)),D[n]);t&&t.latency!==o.value&&(o.value=t.latency,o.entries=t.entries,r())},d=c("event",s,{durationThreshold:null!==(t=n.durationThreshold)&&void 0!==t?t:40});r=u(e,o,M,n.reportAllChanges),d&&("PerformanceEventTiming"in window&&"interactionId"in PerformanceEventTiming.prototype&&d.observe({type:"first-input",buffered:!0}),f((function(){s(d.takeRecords()),o.value<0&&B()>0&&(o.value=0,o.entries=[]),r(!0)})),i((function(){D=[],k=F(),o=a("INP"),r=u(e,o,M,n.reportAllChanges)})))}))},H=[2500,4e3],R={},O=function(e,n){n=n||{},g((function(){var t,r=m(),l=a("LCP"),v=function(e){var n=e[e.length-1];n&&n.startTime<r.firstHiddenTime&&(l.value=Math.max(n.startTime-o(),0),l.entries=[n],t())},p=c("largest-contentful-paint",v);if(p){t=u(e,l,H,n.reportAllChanges);var h=d((function(){R[l.id]||(v(p.takeRecords()),p.disconnect(),R[l.id]=!0,t(!0))}));["keydown","click"].forEach((function(e){addEventListener(e,(function(){return setTimeout(h,0)}),!0)})),f(h),i((function(i){l=a("LCP"),t=u(e,l,H,n.reportAllChanges),s((function(){l.value=performance.now()-i.timeStamp,R[l.id]=!0,t(!0)}))}))}}))},q=[800,1800],_=function e(n){document.prerendering?g((function(){return e(n)})):"complete"!==document.readyState?addEventListener("load",(function(){return e(n)}),!0):setTimeout(n,0)},j=function(e,n){n=n||{};var t=a("TTFB"),c=u(e,t,q,n.reportAllChanges);_((function(){var s=r();if(s){var f=s.responseStart;if(f<=0||f>performance.now())return;t.value=Math.max(f-o(),0),t.entries=[s],c(!0),i((function(){t=a("TTFB",0),(c=u(e,t,q,n.reportAllChanges))(!0)}))}}))};e.CLSThresholds=w,e.FCPThresholds=T,e.FIDThresholds=b,e.INPThresholds=M,e.LCPThresholds=H,e.TTFBThresholds=q,e.getCLS=C,e.getFCP=y,e.getFID=E,e.getINP=V,e.getLCP=O,e.getTTFB=j,e.onCLS=C,e.onFCP=y,e.onFID=E,e.onINP=V,e.onLCP=O,e.onTTFB=j}(this.webVitals=this.webVitals||{});
