import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CustomerServiceTwoToneSvg from "@ant-design/icons-svg/es/asn/CustomerServiceTwoTone";
import AntdIcon from "../components/AntdIcon";
var CustomerServiceTwoTone = function CustomerServiceTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CustomerServiceTwoToneSvg
  }));
};

/**![customer-service](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY5NiA2MzJoMTI4djE5Mkg2OTZ6bS00OTYgMGgxMjh2MTkySDIwMHoiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTUxMiAxMjhjLTIxMi4xIDAtMzg0IDE3MS45LTM4NCAzODR2MzYwYzAgMTMuMyAxMC43IDI0IDI0IDI0aDE4NGMzNS4zIDAgNjQtMjguNyA2NC02NFY2MjRjMC0zNS4zLTI4LjctNjQtNjQtNjRIMjAwdi00OGMwLTE3Mi4zIDEzOS43LTMxMiAzMTItMzEyczMxMiAxMzkuNyAzMTIgMzEydjQ4SDY4OGMtMzUuMyAwLTY0IDI4LjctNjQgNjR2MjA4YzAgMzUuMyAyOC43IDY0IDY0IDY0aDE4NGMxMy4zIDAgMjQtMTAuNyAyNC0yNFY1MTJjMC0yMTIuMS0xNzEuOS0zODQtMzg0LTM4NHpNMzI4IDYzMnYxOTJIMjAwVjYzMmgxMjh6bTQ5NiAxOTJINjk2VjYzMmgxMjh2MTkyeiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(CustomerServiceTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CustomerServiceTwoTone';
}
export default RefIcon;