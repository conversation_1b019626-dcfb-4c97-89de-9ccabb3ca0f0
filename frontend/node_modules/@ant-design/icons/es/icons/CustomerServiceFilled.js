import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CustomerServiceFilledSvg from "@ant-design/icons-svg/es/asn/CustomerServiceFilled";
import AntdIcon from "../components/AntdIcon";
var CustomerServiceFilled = function CustomerServiceFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CustomerServiceFilledSvg
  }));
};

/**![customer-service](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiAxMjhjLTIxMi4xIDAtMzg0IDE3MS45LTM4NCAzODR2MzYwYzAgMTMuMyAxMC43IDI0IDI0IDI0aDE4NGMzNS4zIDAgNjQtMjguNyA2NC02NFY2MjRjMC0zNS4zLTI4LjctNjQtNjQtNjRIMjAwdi00OGMwLTE3Mi4zIDEzOS43LTMxMiAzMTItMzEyczMxMiAxMzkuNyAzMTIgMzEydjQ4SDY4OGMtMzUuMyAwLTY0IDI4LjctNjQgNjR2MjA4YzAgMzUuMyAyOC43IDY0IDY0IDY0aDE4NGMxMy4zIDAgMjQtMTAuNyAyNC0yNFY1MTJjMC0yMTIuMS0xNzEuOS0zODQtMzg0LTM4NHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(CustomerServiceFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CustomerServiceFilled';
}
export default RefIcon;