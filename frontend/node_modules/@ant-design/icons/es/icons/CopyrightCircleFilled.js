import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CopyrightCircleFilledSvg from "@ant-design/icons-svg/es/asn/CopyrightCircleFilled";
import AntdIcon from "../components/AntdIcon";
var CopyrightCircleFilled = function CopyrightCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CopyrightCircleFilledSvg
  }));
};

/**![copyright-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em01LjQgNjcwYy0xMTAgMC0xNzMuNC03My4yLTE3My40LTE5NC45di01Mi4zQzM0NCAzNjQuMiA0MDcuNCAyOTAgNTE3LjMgMjkwYzk0LjMgMCAxNjIuNyA2MC43IDE2Mi43IDE0Ny40IDAgMi42LTIuMSA0LjctNC43IDQuN2gtNTYuN2MtNC4yIDAtNy42LTMuMi04LTcuNC00LTQ5LjUtNDAtODMuNC05My04My40LTY1LjMgMC0xMDIuMSA0OC41LTEwMi4xIDEzNS41djUyLjZjMCA4NS43IDM2LjkgMTMzLjYgMTAyLjEgMTMzLjYgNTIuOCAwIDg4LjctMzEuNyA5My03Ny44LjQtNC4xIDMuOC03LjMgOC03LjNoNTYuOGMyLjYgMCA0LjcgMi4xIDQuNyA0LjcgMCA4Mi42LTY4LjcgMTQxLjQtMTYyLjcgMTQxLjR6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(CopyrightCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CopyrightCircleFilled';
}
export default RefIcon;