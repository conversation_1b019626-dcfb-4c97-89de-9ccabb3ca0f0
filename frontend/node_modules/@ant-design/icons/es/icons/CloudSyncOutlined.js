import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CloudSyncOutlinedSvg from "@ant-design/icons-svg/es/asn/CloudSyncOutlined";
import AntdIcon from "../components/AntdIcon";
var CloudSyncOutlined = function CloudSyncOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CloudSyncOutlinedSvg
  }));
};

/**![cloud-sync](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgxMS40IDM2OC45Qzc2NS42IDI0OCA2NDguOSAxNjIgNTEyLjIgMTYyUzI1OC44IDI0Ny45IDIxMyAzNjguOEMxMjYuOSAzOTEuNSA2My41IDQ3MC4yIDY0IDU2My42IDY0LjYgNjY4IDE0NS42IDc1Mi45IDI0Ny42IDc2MmM0LjcuNCA4LjctMy4zIDguNy04di02MC40YzAtNC0zLTcuNC03LTcuOS0yNy0zLjQtNTIuNS0xNS4yLTcyLjEtMzQuNS0yNC0yMy41LTM3LjItNTUuMS0zNy4yLTg4LjYgMC0yOCA5LjEtNTQuNCAyNi4yLTc2LjQgMTYuNy0yMS40IDQwLjItMzYuOSA2Ni4xLTQzLjdsMzcuOS0xMCAxMy45LTM2LjdjOC42LTIyLjggMjAuNi00NC4yIDM1LjctNjMuNSAxNC45LTE5LjIgMzIuNi0zNiA1Mi40LTUwIDQxLjEtMjguOSA4OS41LTQ0LjIgMTQwLTQ0LjJzOTguOSAxNS4zIDE0MCA0NC4zYzE5LjkgMTQgMzcuNSAzMC44IDUyLjQgNTAgMTUuMSAxOS4zIDI3LjEgNDAuNyAzNS43IDYzLjVsMTMuOCAzNi42IDM3LjggMTBjNTQuMiAxNC40IDkyLjEgNjMuNyA5Mi4xIDEyMCAwIDMzLjYtMTMuMiA2NS4xLTM3LjIgODguNi0xOS41IDE5LjItNDQuOSAzMS4xLTcxLjkgMzQuNS00IC41LTYuOSAzLjktNi45IDcuOVY3NTRjMCA0LjcgNC4xIDguNCA4LjggOCAxMDEuNy05LjIgMTgyLjUtOTQgMTgzLjItMTk4LjIuNi05My40LTYyLjctMTcyLjEtMTQ4LjYtMTk0Ljl6IiAvPjxwYXRoIGQ9Ik0zNzYuOSA2NTYuNGMxLjgtMzMuNSAxNS43LTY0LjcgMzkuNS04OC42IDI1LjQtMjUuNSA2MC0zOS44IDk2LTM5LjggMzYuMiAwIDcwLjMgMTQuMSA5NiAzOS44IDEuNCAxLjQgMi43IDIuOCA0LjEgNC4zbC0yNSAxOS42YTggOCAwIDAwMyAxNC4xbDk4LjIgMjRjNSAxLjIgOS45LTIuNiA5LjktNy43bC41LTEwMS4zYzAtNi43LTcuNi0xMC41LTEyLjktNi4zTDY2MyA1MzIuN2MtMzYuNi00Mi05MC40LTY4LjYtMTUwLjUtNjguNi0xMDcuNCAwLTE5NSA4NS4xLTE5OS40IDE5MS43LS4yIDQuNSAzLjQgOC4zIDggOC4zSDM2OWM0LjItLjEgNy43LTMuNCA3LjktNy43ek03MDMgNjY0aC00Ny45Yy00LjIgMC03LjcgMy4zLTggNy42LTEuOCAzMy41LTE1LjcgNjQuNy0zOS41IDg4LjYtMjUuNCAyNS41LTYwIDM5LjgtOTYgMzkuOC0zNi4yIDAtNzAuMy0xNC4xLTk2LTM5LjgtMS40LTEuNC0yLjctMi44LTQuMS00LjNsMjUtMTkuNmE4IDggMCAwMC0zLTE0LjFsLTk4LjItMjRjLTUtMS4yLTkuOSAyLjYtOS45IDcuN2wtLjQgMTAxLjRjMCA2LjcgNy42IDEwLjUgMTIuOSA2LjNsMjMuMi0xOC4yYzM2LjYgNDIgOTAuNCA2OC42IDE1MC41IDY4LjYgMTA3LjQgMCAxOTUtODUuMSAxOTkuNC0xOTEuNy4yLTQuNS0zLjQtOC4zLTgtOC4zeiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(CloudSyncOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CloudSyncOutlined';
}
export default RefIcon;