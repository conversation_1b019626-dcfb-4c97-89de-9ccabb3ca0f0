import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ApiFilledSvg from "@ant-design/icons-svg/es/asn/ApiFilled";
import AntdIcon from "../components/AntdIcon";
var ApiFilled = function ApiFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: ApiFilledSvg
  }));
};

/**![api](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkxNy43IDE0OC44bC00Mi40LTQyLjRjLTEuNi0xLjYtMy42LTIuMy01LjctMi4zcy00LjEuOC01LjcgMi4zbC03Ni4xIDc2LjFhMTk5LjI3IDE5OS4yNyAwIDAwLTExMi4xLTM0LjNjLTUxLjIgMC0xMDIuNCAxOS41LTE0MS41IDU4LjZMNDMyLjMgMzA4LjdhOC4wMyA4LjAzIDAgMDAwIDExLjNMNzA0IDU5MS43YzEuNiAxLjYgMy42IDIuMyA1LjcgMi4zIDIgMCA0LjEtLjggNS43LTIuM2wxMDEuOS0xMDEuOWM2OC45LTY5IDc3LTE3NS43IDI0LjMtMjUzLjVsNzYuMS03Ni4xYzMuMS0zLjIgMy4xLTguMyAwLTExLjR6TTU3OC45IDU0Ni43YTguMDMgOC4wMyAwIDAwLTExLjMgMEw1MDEgNjEzLjMgNDEwLjcgNTIzbDY2LjctNjYuN2MzLjEtMy4xIDMuMS04LjIgMC0xMS4zTDQ0MSA0MDguNmE4LjAzIDguMDMgMCAwMC0xMS4zIDBMMzYzIDQ3NS4zbC00My00M2E3Ljg1IDcuODUgMCAwMC01LjctMi4zYy0yIDAtNC4xLjgtNS43IDIuM0wyMDYuOCA1MzQuMmMtNjguOSA2OC45LTc3IDE3NS43LTI0LjMgMjUzLjVsLTc2LjEgNzYuMWE4LjAzIDguMDMgMCAwMDAgMTEuM2w0Mi40IDQyLjRjMS42IDEuNiAzLjYgMi4zIDUuNyAyLjNzNC4xLS44IDUuNy0yLjNsNzYuMS03Ni4xYzMzLjcgMjIuOSA3Mi45IDM0LjMgMTEyLjEgMzQuMyA1MS4yIDAgMTAyLjQtMTkuNSAxNDEuNS01OC42bDEwMS45LTEwMS45YzMuMS0zLjEgMy4xLTguMiAwLTExLjNsLTQzLTQzIDY2LjctNjYuN2MzLjEtMy4xIDMuMS04LjIgMC0xMS4zbC0zNi42LTM2LjJ6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(ApiFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ApiFilled';
}
export default RefIcon;