"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _FolderViewOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/FolderViewOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var FolderViewOutlined = function FolderViewOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _FolderViewOutlined.default
  }));
};

/**![folder-view](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik0zMDkuMSA1NTQuM2E0Mi45MiA0Mi45MiAwIDAwMCAzNi40QzM1My4zIDY4NCA0MjEuNiA3MzIgNTEyLjUgNzMyczE1OS4yLTQ4LjEgMjAzLjQtMTQxLjNjNS40LTExLjUgNS40LTI0LjguMS0zNi4zbC0uMS0uMS0uMS0uMUM2NzEuNyA0NjEgNjAzLjQgNDEzIDUxMi41IDQxM3MtMTU5LjIgNDguMS0yMDMuNCAxNDEuM3pNNTEyLjUgNDc3YzYyLjEgMCAxMDcuNCAzMCAxNDEuMSA5NS41QzYyMCA2MzggNTc0LjYgNjY4IDUxMi41IDY2OHMtMTA3LjQtMzAtMTQxLjEtOTUuNWMzMy43LTY1LjUgNzktOTUuNSAxNDEuMS05NS41eiIgLz48cGF0aCBkPSJNNDU3IDU3M2E1NiA1NiAwIDEwMTEyIDAgNTYgNTYgMCAxMC0xMTIgMHoiIC8+PHBhdGggZD0iTTg4MCAyOTguNEg1MjFMNDAzLjcgMTg2LjJhOC4xNSA4LjE1IDAgMDAtNS41LTIuMkgxNDRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjU5MmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg3MzZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzMwLjRjMC0xNy43LTE0LjMtMzItMzItMzJ6TTg0MCA3NjhIMTg0VjI1NmgxODguNWwxMTkuNiAxMTQuNEg4NDBWNzY4eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(FolderViewOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FolderViewOutlined';
}
var _default = exports.default = RefIcon;