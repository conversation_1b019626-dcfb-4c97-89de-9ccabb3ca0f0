<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 360 650"><defs/><g fill="none" fill-rule="evenodd"><path d="M110.1 71h60v20h-60z"/><text fill="#000" font-family="Helvetica" font-size="12" transform="translate(111.5 73.5)"><tspan x="0" y="11">index.js</tspan></text><path stroke="#666" d="M80.1 63v18h10"/><path fill="#434445" fill-rule="nonzero" stroke="#000" d="M51.7 1c-.3 0-.5.1-.6.3l-1 1.6v17c0 .6.3 1.1 1.1 1.1h22.1c.5 0 .6-.1.7-.5l2.3-12.2c.1-.4-.1-.8-.6-.8h-1.8v-4c0-.5-.2-.9-.9-.9H60.6l-.9-1.3c-.1-.2-.3-.3-.6-.3h-7.4zm0 .8h7.4l1 1.6h13v4.1h-7.8c-.4 0-.6.2-.8.4l-.7 1.4H53.3c-.5 0-.7.2-.8.6L51 18.7V3.2l.8-1.4zm13.5 6.5h10.3l-2.3 11.9H51.4l1.9-10.1h11l1-1.8z"/><path d="M76.4 1h70v20h-70z"/><text fill="#000" font-family="Helvetica" font-size="12" transform="translate(81.5 3.5)"><tspan x="0" y="11">my-project</tspan></text><path d="M70.1 41h26.3v20H70.1z"/><path fill="#434445" fill-rule="nonzero" stroke="#000" d="M71.7 41c-.3 0-.5.1-.6.3l-1 1.6v17c0 .6.3 1.1 1.1 1.1h22.1c.5 0 .6-.1.7-.5l2.3-12.2c.1-.4-.1-.8-.6-.8h-1.8v-4c0-.5-.2-.9-.9-.9H80.6l-.9-1.3c-.1-.2-.3-.3-.6-.3h-7.4zm0 .8h7.4l1 1.6h13v4.1h-7.8c-.4 0-.6.2-.8.4l-.7 1.4H73.3c-.5 0-.7.2-.8.6L71 58.7V43.1l.8-1.3zm13.5 6.5h10.3l-2.3 11.9H71.4l1.9-10.1h11l1-1.8z"/><path d="M96.4 41h30v20h-30z"/><text fill="#000" font-family="Helvetica" font-size="12" transform="translate(100 44)"><tspan x="0" y="11">src</tspan></text><path stroke="#666" d="M60.1 23v28h8M80.1 86v45h10"/><path d="M113.3 121h70v20h-70z"/><text fill="#000" font-family="Helvetica" font-size="12" transform="translate(114.5 123.5)"><tspan x="0" y="11">styles.scss</tspan></text><path fill="#F0F0F0" fill-rule="nonzero" d="M99.6 141h203.7v20H99.6z"/><text fill="#000" font-family="CourierNewPSMT, Courier New" font-size="9" transform="translate(105.5 147.5)"><tspan x="0" y="7">@import &quot;foo/partial&quot;;</tspan></text><path d="M130.2 201h80v20h-80z"/><text fill="#000" font-family="Helvetica" font-size="12" transform="translate(131.5 203.5)"><tspan x="0" y="11">_partial.scss</tspan></text><path fill="#F0F0F0" fill-rule="nonzero" d="M116.5 221h203.7v60H116.5z"/><text fill="#000" font-family="CourierNewPSMT, Courier New" font-size="9" transform="translate(123 227)"><tspan x=".7" y="7.5">@import &quot;bar/mixins&quot;;</tspan>  <tspan x=".7" y="27.5">.cool {</tspan>  <tspan x=".7" y="47.5">}</tspan></text><text fill="#000" font-family="CourierNewPSMT, Courier New" font-size="9" transform="translate(138 258)"><tspan x="0" y="7">@include cool-background-image;</tspan></text><path d="M90.2 171h26.3v20H90.2z"/><path fill="#434445" fill-rule="nonzero" stroke="#000" d="M91.8 171c-.3 0-.5.1-.6.3l-1 1.6v17c0 .6.3 1.1 1.1 1.1h22.2c.4 0 .5-.2.6-.5l2.3-12.2c.1-.4-.1-.8-.6-.8H114v-4c0-.5-.2-.9-.8-.9h-12.5l-.9-1.3c-.1-.2-.3-.3-.6-.3h-7.4zm0 .8h7.4l1 1.6h13v4.1h-7.8c-.4 0-.6.2-.7.4l-.8 1.4H93.4c-.5 0-.7.2-.8.6l-1.6 8.8v-15.5l.8-1.4zm13.5 6.5h10.3l-2.3 11.9H91.5l2-10.1h10.8l1-1.8z"/><path d="M116.5 171h40v20h-40z"/><text fill="#000" font-family="Helvetica" font-size="12" transform="translate(120.5 173.5)"><tspan x="0" y="11">foo</tspan></text><path stroke="#666" d="M110.2 211h-10v-18M80.2 136v45h8M108 301h-8v-84"/><path d="M110 291h26.3v20H110z"/><path fill="#434445" fill-rule="nonzero" stroke="#000" d="M111.6 291c-.3 0-.5.1-.6.3l-1 1.6v17c0 .6.3 1.1 1.1 1.1h22.2c.4 0 .5-.1.6-.5l2.3-12.2c.1-.4-.1-.8-.6-.8h-1.8v-4c0-.5-.2-.9-.9-.9h-12.4l-.9-1.3c-.1-.2-.3-.3-.6-.3h-7.4zm0 .8h7.4l1 1.6h13v4.1h-7.8c-.4 0-.6.2-.7.4l-.8 1.4h-10.5c-.5 0-.7.2-.8.6l-1.6 8.8v-15.6l.8-1.3zm13.6 6.5h10.2l-2.3 11.9h-21.8l2-10.1H124l1-1.8z"/><path d="M136.3 291h40v20h-40z"/><text fill="#000" font-family="Helvetica" font-size="12" transform="translate(139.5 293.5)"><tspan x="0" y="11">bar</tspan></text><path d="M149.5 321h80v20h-80z"/><text fill="#000" font-family="Helvetica" font-size="12" transform="translate(150.5 323.5)"><tspan x="0" y="11">_mixins.scss</tspan></text><path fill="#F0F0F0" fill-rule="nonzero" d="M135.8 341H350v60H135.8z"/><path stroke="#666" d="M129.5 331h-10v-18M129 421h-9v-84"/><path d="M130.5 411h26.3v20h-26.3z"/><path fill="#434445" fill-rule="nonzero" stroke="#000" d="M132 411c-.2 0-.4.1-.5.3l-1 1.6v17c0 .6.3 1.1 1 1.1h22.2c.5 0 .6-.1.6-.5l2.4-12.2c0-.4-.1-.8-.6-.8h-1.8v-4c0-.5-.3-.9-.9-.9H141l-.8-1.3c-.1-.2-.3-.3-.6-.3H132zm.1.8h7.4l1 1.6h13v4.1h-7.8c-.4 0-.6.2-.8.4l-.8 1.4h-10.5c-.4 0-.6.2-.7.6l-1.6 8.8v-15.6l.8-1.3zm13.5 6.5H156l-2.3 11.9h-21.8l1.9-10.1h11l1-1.8z"/><path d="M156.8 411h30v20h-30z"/><text fill="#000" font-family="Helvetica" font-size="12" transform="translate(160.5 413.5)"><tspan x="0" y="11">baz</tspan></text><path d="M169.9 441h100v20h-100z"/><text fill="#000" font-family="Helvetica" font-size="12" transform="translate(171.5 443.5)"><tspan x="0" y="11">_functions.scss</tspan></text><path fill="#F0F0F0" fill-rule="nonzero" d="M156.3 461h203.8v40H156.3z"/><text fill="#000" font-family="CourierNewPSMT, Courier New" font-size="9" transform="translate(161.5 467.5)"><tspan x="0" y="7">@function get-url($temp) {</tspan>  <tspan x="0" y="27">}</tspan></text><text fill="#000" font-family="CourierNewPSMT, Courier New" font-size="9" transform="translate(178 478)"><tspan x="0" y="7">@return url(#($temp).png)</tspan></text><path stroke="#666" d="M149.9 451H140l-.1-18M150 521h-10v-65"/><path d="M170 511h60v20h-60z"/><text fill="#000" font-family="Helvetica" font-size="12" transform="translate(171.5 513.5)"><tspan x="0" y="11">cool.png</tspan></text><path stroke="#666" d="M130 551h-10V426"/><path d="M150 541h60v20h-60z"/><text fill="#000" font-family="Helvetica" font-size="12" transform="translate(151.5 543.5)"><tspan x="0" y="11">cool.png</tspan></text><path stroke="#666" d="M109.7 581H100V306"/><path d="M129.7 571h60v20h-60z"/><text fill="#000" font-family="Helvetica" font-size="12" transform="translate(130.5 573.5)"><tspan x="0" y="11">cool.png</tspan></text><path fill="#B3B3B3" fill-rule="nonzero" d="M111.7 600h70v20h-70z"/><text fill="#FFF" font-family="Helvetica" font-size="12" transform="translate(116.5 602.5)"><tspan x="0" y="11">cool.png</tspan></text><path fill="#FFF" fill-opacity=".5" fill-rule="nonzero" d="M89.7 600h20v20h-20z"/><path stroke="#666" d="M90 610H80V186"/><path stroke="#666" stroke-dasharray="3" d="M59 141C20.3 207.7 1 281 1 361s18.2 157.6 54.5 232.7"/><path stroke="#666" d="M58.5 600l-6.2-4.8 6.3-3z"/><path fill="#F0F0F0" fill-rule="nonzero" d="M100 91h203.7v20H100z"/><text fill="#000" font-family="CourierNewPSMT, Courier New" font-size="10" transform="translate(105 96)"><tspan x="0" y="8">import &apos;foo/styles.scss&apos;;</tspan></text><path fill="#000" fill-rule="nonzero" d="M104.8 82.7v.9l-.1.3-.3.1h-8.8l-.3-.1-.1-.3v-.9l.1-.2.3-.2h8.8c.1 0 .2 0 .3.2l.1.2zm-3.1-3.3v1.1l-.3.2h-6l-.1-.2-.1-.3v-.8-.3l.3-.1h6l.1.1.1.3zm-8.1 8h12.8v-10h-5.2c-.3 0-.6-.2-.9-.4-.2-.3-.3-.6-.3-1v-5.3h-6.4v16.6zm8-11.7h3.7l-3.7-4v4zm6.4 1.6v10.4c0 .4-.1.7-.3 1l-.9.3H93.2c-.3 0-.6-.1-.8-.4-.3-.2-.4-.5-.4-.9V70.2c0-.3.1-.6.4-.8.2-.3.5-.4.8-.4h6.8c.3 0 .7 0 1.1.3.4.1.7.3 1 .6l5 5.3a3.1 3.1 0 01.8 2.1zM124.8 211.8v.8l-.1.3-.3.1h-8.8l-.3-.1-.1-.3v-.9l.1-.2.3-.2h8.8c.1 0 .2 0 .3.2l.1.2zm-3.1-3.4v1.1l-.3.2h-6l-.1-.2-.1-.2v-.9-.3l.3-.1h6l.1.1.1.3zm-8.1 8h12.8v-10h-5.2c-.3 0-.6-.2-.9-.4-.2-.3-.3-.6-.3-1v-5.3h-6.4v16.6zm8-11.7h3.7l-3.7-4v4zm6.4 1.6v10.4c0 .4-.1.7-.3 1l-.9.3h-13.6c-.3 0-.6-.1-.8-.4-.3-.2-.4-.5-.4-.9v-17.4c0-.4.1-.7.4-1 .2-.2.5-.3.8-.3h6.8c.3 0 .7 0 1.1.3.4.1.7.3 1 .6l5 5.3a3.1 3.1 0 01.8 2.1zM143.8 331.8v.8l-.1.3-.3.1h-8.8l-.3-.1-.1-.3v-.9l.1-.2.3-.2h8.8c.1 0 .2 0 .3.2l.1.3zm-3.1-3.4v1.1l-.3.2h-6l-.1-.2-.1-.3v-.8-.3l.3-.1h6l.1.1.1.3zm-8.1 8h12.8v-10h-5.2c-.3 0-.6-.2-.8-.4-.3-.3-.4-.6-.4-1v-5.3h-6.4v16.6zm8-11.7h3.7l-3.7-4v4zm6.4 1.6v10.4c0 .4-.1.7-.3 1l-.9.3h-13.6c-.3 0-.6-.1-.8-.4-.3-.2-.4-.5-.4-.9v-17.5c0-.3.1-.6.4-.8.2-.3.5-.4.8-.4h6.8c.3 0 .7 0 1.1.3.4.1.7.3 1 .6l5 5.3a3.1 3.1 0 01.8 2.1zM163.8 451.8v.8l-.1.3-.3.1h-8.8l-.3-.1-.1-.3v-.9l.1-.2.3-.2h8.8c.1 0 .2 0 .3.2l.1.3zm-3.1-3.4v1.1l-.3.2h-6l-.1-.2-.1-.3v-.8-.3l.3-.1h6l.1.1.1.3zm-8.1 8h12.8v-10h-5.2c-.3 0-.6-.2-.8-.4-.3-.3-.4-.6-.4-1v-5.3h-6.4v16.6zm8-11.7h3.7l-3.7-4v4zm6.4 1.6v10.4c0 .4-.1.7-.3 1l-.9.3h-13.6c-.3 0-.6-.1-.8-.4-.3-.2-.4-.5-.4-.9v-17.5c0-.3.1-.6.4-.8.2-.3.5-.4.8-.4h6.8c.3 0 .7 0 1.1.3.4.1.7.3 1 .6l5 5.3a3.1 3.1 0 01.8 2.1zM164.8 523.8v.8l-.1.3-.3.1h-8.8l-.3-.1-.1-.3v-.9l.1-.2.3-.2h8.8c.1 0 .2 0 .3.2l.1.2zm-3.1-3.4v1.1l-.3.2h-6l-.1-.2-.1-.2v-.9-.3l.3-.1h6l.1.1.1.3zm-8.1 8h12.8v-10h-5.2c-.3 0-.6-.2-.8-.4-.3-.3-.4-.6-.4-1v-5.3h-6.4v16.6zm8-11.7h3.7l-3.7-4v4zm6.4 1.6v10.5a1.1 1.1 0 01-1.2 1.3l-13.6-.1c-.3 0-.6-.1-.8-.4-.3-.2-.4-.5-.4-.9v-17.5c0-.3.1-.6.4-.8.2-.3.5-.4.8-.4h6.8c.3 0 .7 0 1.1.3.4.1.7.3 1 .6l5 5.3a3.1 3.1 0 01.8 2.1zM144.8 553.8v.8l-.1.3-.3.1h-8.8l-.3-.1-.1-.3v-.9l.1-.2.3-.2h8.8c.1 0 .2 0 .3.2l.1.2zm-3.1-3.4v1.1l-.3.2h-6l-.1-.2-.1-.2v-.9-.3l.3-.1h6l.1.1.1.3zm-8.1 8h12.8v-10h-5.2c-.3 0-.6-.2-.8-.4-.3-.3-.4-.6-.4-1v-5.3h-6.4v16.6zm8-11.7h3.7l-3.7-4v4zm6.4 1.6v10.5a1.1 1.1 0 01-1.2 1.3l-13.6-.1c-.3 0-.6-.1-.8-.4-.3-.2-.4-.5-.4-.9v-17.5c0-.3.1-.6.4-.8.2-.3.5-.4.8-.4h6.8c.3 0 .7 0 1.1.3.4.1.7.3 1 .6l5 5.3a3.1 3.1 0 01.8 2.1zM124.8 582.8v.8l-.1.3-.3.1h-8.8l-.3-.1-.1-.3v-.9l.1-.2.3-.2h8.8c.1 0 .2 0 .3.2l.1.2zm-3.1-3.4v1.1l-.3.2h-6l-.1-.2-.1-.2v-.9-.3l.3-.1h6l.1.1.1.3zm-8.1 8h12.8v-10h-5.2c-.3 0-.6-.2-.9-.4-.2-.3-.3-.6-.3-1v-5.3h-6.4v16.6zm8-11.7h3.7l-3.7-4v4zm6.4 1.6v10.5a1.1 1.1 0 01-1.2 1.3l-13.6-.1c-.3 0-.6-.1-.8-.4-.3-.2-.4-.5-.4-.9v-17.5c0-.3.1-.6.4-.8.2-.3.5-.4.8-.4h6.8c.3 0 .7 0 1.1.3.4.1.7.3 1 .6l5 5.3a3.1 3.1 0 01.8 2.1zM104.8 132.8v.8l-.1.3-.3.1h-8.8l-.3-.1-.1-.3v-.9l.1-.2.3-.2h8.8c.1 0 .2 0 .3.2l.1.2zm-3.1-3.4v1.1l-.3.2h-6l-.1-.2-.1-.2v-.9-.3l.3-.1h6l.1.1.1.3zm-8.1 8h12.8v-10h-5.2c-.3 0-.6-.2-.9-.4-.2-.3-.3-.6-.3-1v-5.3h-6.4v16.6zm8-11.7h3.7l-3.7-4v4zm6.4 1.6v10.5a1.1 1.1 0 01-1.2 1.3l-13.6-.1c-.3 0-.6-.1-.8-.4-.3-.2-.4-.5-.4-.9v-17.4c0-.4.1-.7.4-1 .2-.2.5-.3.8-.3h6.8c.3 0 .7 0 1.1.3.4.1.7.3 1 .6l5 5.3a3.1 3.1 0 01.8 2.1z"/><path fill="#B3B3B3" fill-rule="nonzero" d="M104.8 613.8v.8l-.1.3-.3.1h-8.8l-.3-.1-.1-.3v-.9l.1-.2.3-.2h8.8c.1 0 .2 0 .3.2l.1.2zm-3.1-3.4v1.1l-.3.2h-6l-.1-.2-.1-.2v-.9-.3l.3-.1h6l.1.1.1.3zm-8.1 8h12.8v-10h-5.2c-.3 0-.6-.2-.9-.4-.2-.3-.3-.6-.3-1v-5.3h-6.4v16.6zm8-11.7h3.7l-3.7-4v4zm6.4 1.6v10.5a1.1 1.1 0 01-1.2 1.3l-13.6-.1c-.3 0-.6-.1-.8-.4-.3-.2-.4-.5-.4-.9v-17.5c0-.3.1-.6.4-.8.2-.3.5-.4.8-.4h6.8c.3 0 .7 0 1.1.3.4.1.7.3 1 .6l5 5.3a3.1 3.1 0 01.8 2.1z"/><g fill-rule="nonzero"><path fill="#FFF" d="M13 625l11 6.2v12.6L13 650l-11-6.2v-12.6z"/><path fill="#8ED6FB" d="M22.4 644l-8.5 5v-4l5.3-3 3.2 2zm.6-.6v-10.5l-3.1 1.8v6.8l3.1 1.9zm-18.4.6l8.5 5v-4l-5.3-3-3.2 2zm-.6-.6v-10.5l3.1 1.8v6.8L4 643.4zm.4-11.2L13 627v3.8L7.5 634l-3.1-1.8zm18.2 0L14 627v3.8l5.6 3.2 3.1-1.8z"/><path fill="#1C78C0" d="M13.6 644L8 641v-6.2l5.6 3.2v6zm.8 0l5.6-3v-6.2l-5.6 3.2v6zm-6-9.9L14 631l5.6 3.1-5.6 3.2-5.6-3.2z"/><path fill="#1A1C1C" d="M35.8 641.3H38l2.5-7.4h-2.3l-1.5 5.3-1.5-5.3h-2l-1.6 5.3-1.4-5.3H28l2.4 7.4h2.4l1.5-4.8 1.5 4.8zm4.9-3.7c0 2.3 1.6 4 4.2 4 2 0 3.1-1.2 3.4-1.7l-1.4-1c-.2.3-.8.9-2 .9-1.1 0-2-.7-2-1.7h5.7v-.4c0-2.5-1.5-4-4-4-2.3 0-4 1.7-4 4zm2.2-.8c.1-.9.8-1.5 1.8-1.5s1.7.6 1.7 1.5h-3.5zm6.5 4.5h2v-.7c.2.2 1 1 2.4 1 2.3 0 4-1.7 4-4s-1.6-4-4-4c-1.3 0-2.1.8-2.3 1V631h-2.1v10.3zm2-3.7c0-1.4 1-2.2 2.1-2.2 1.2 0 2 1 2 2.2 0 1.3-.8 2.2-2 2.2-1.3 0-2-1-2-2.2zm7.2 6.4h2v-3.4c.3.2 1 1 2.5 1 2.3 0 3.8-1.7 3.8-4s-1.6-4-4-4c-1.3 0-2.1.8-2.3 1v-.7h-2V644zm2-6.4c0-1.2.8-2.2 2-2.2 1.3 0 2.2.8 2.2 2.2 0 1.3-.9 2.2-2.1 2.2-1.2 0-2-.8-2-2.2zm6.7 1.4c0 1.6 1.4 2.5 3 2.5 1 0 1.6-.2 2-.7l.1.5h1.9v-4.7c0-1.7-.8-3-3.4-3-1.2 0-2.4.5-3.1 1l.8 1.3a5 5 0 012.1-.5c1 0 1.5.4 1.5 1v.6c-.3-.2-1-.5-1.8-.5-1.9 0-3.1 1-3.1 2.5zm2.1 0c0-.7.6-1 1.4-1 .8 0 1.4.3 1.4 1 0 .6-.6 1-1.4 1-.8 0-1.4-.4-1.4-1zm10 .8c-1.3 0-2.2-1-2.2-2.2 0-1.2.8-2.2 2.2-2.2 1 0 1.5.4 1.9.7l.6-1.6c-.5-.4-1.4-.8-2.6-.8-2.4 0-4.3 1.6-4.3 4 0 2.2 1.8 3.8 4.4 3.8 1.1 0 2-.4 2.5-.8l-.6-1.6a3 3 0 01-1.8.7zm3.3 1.5h2.1v-3.8l2.7 3.8H90l-3-4 2.8-3.4h-2.4l-2.6 3.1v-6h-2.1v10.3z"/></g><text fill="#000" font-family="CourierNewPSMT, Courier New" font-size="9" transform="translate(1 1)"><tspan x="141" y="352">@import &quot;baz/functions&quot;;</tspan>  <tspan x="141" y="372">@mixin cool-background-image {</tspan>  <tspan x="141" y="392">}</tspan></text><text fill="#000" font-family="CourierNewPSMT, Courier New" font-size="9" transform="translate(1 1)"><tspan x="155" y="382">background-image: get-url(&quot;cool&quot;);</tspan></text></g></svg>
