package com.library.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "email_notifications")
public class EmailNotification extends BaseEntity {
    
    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;
    
    @NotBlank
    @Column(length = 100)
    private String email;
    
    @NotBlank
    @Column(length = 200)
    private String subject;
    
    @NotBlank
    @Lob
    private String content;
    
    @NotBlank
    @Column(length = 50)
    private String type; // RESERVATION_AVAILABLE, OVERDUE_REMINDER, RETURN_REMINDER等
    
    @Column(columnDefinition = "TINYINT DEFAULT 0")
    private Integer status = 0; // 0-待发送，1-已发送，2-发送失败
    
    @Column(name = "sent_at")
    private LocalDateTime sentAt;
    
    @Lob
    @Column(name = "error_message")
    private String errorMessage;
    
    @Column(name = "retry_count")
    private Integer retryCount = 0;
    
    public String getStatusText() {
        if (status == null) return "未知";
        switch (status) {
            case 0: return "待发送";
            case 1: return "已发送";
            case 2: return "发送失败";
            default: return "未知";
        }
    }

    // Temporary explicit getters/setters while fixing Lombok
    public User getUser() { return user; }
    public void setUser(User user) { this.user = user; }
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    public String getSubject() { return subject; }
    public void setSubject(String subject) { this.subject = subject; }
    public String getContent() { return content; }
    public void setContent(String content) { this.content = content; }
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }
    public Integer getStatus() { return status; }
    public void setStatus(Integer status) { this.status = status; }
    public LocalDateTime getSentAt() { return sentAt; }
    public void setSentAt(LocalDateTime sentAt) { this.sentAt = sentAt; }
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    public Integer getRetryCount() { return retryCount; }
    public void setRetryCount(Integer retryCount) { this.retryCount = retryCount; }
}
